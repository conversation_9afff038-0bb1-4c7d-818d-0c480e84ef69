import prisma from '@/config/prismaClient';
import { ClassApprovalStatus,Prisma} from '@prisma/client';
import { getAverageRatingByClassId, getReviewCountByClassId } from './reviewsService';

export const fetchClassDetailsById = async (id: string) => {
  const classData = await prisma.classes.findUnique({
    where: { id },
    include: {
      ClassAbout: true,
      experience: {
        select: {
          id: true,
          title: true,
          from: true,
          to: true,
          isExperience: true,
          status: true,
          createdAt: true,
        },
      },
      education: {
        select: {
          id: true,
          degree: true,
          university: true,
          passoutYear: true,
          degreeType: true,
          certificate: true,
          isDegree: true,
          status: true,
          createdAt: true,
        },
      },
      certificates: {
        select: {
          id: true,
          title: true,
          certificateUrl: true,
          isCertificate: true,
          status: true,
          createdAt: true,
        },
      },
      status: true,
      tuitionClasses: true,
      address: true, 
    },
  });

  return classData;
};

export const fetchClassDetailsByIdForAdmin = async (id: string) => {
  const classData = await prisma.classes.findUnique({
    where: { id },
    include: {
      ClassAbout: true,
      experience: true,
      education: true,
      certificates: true,
      status: true,
      tuitionClasses: true,
    },
  });

  return classData;
};

export const fetchAllClasses = async (
  page: number,
  limit: number,
  search?: string,
  status?: string
) => {
  const skip = (page - 1) * limit;

  const whereClause: { OR?: any[]; status?: any } = {};

  if (search) {
    whereClause.OR = [
      { firstName: { contains: search, mode: 'insensitive' } },
      { lastName: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { className: { contains: search, mode: 'insensitive' } },
    ];
  }

  if (status && status !== 'all') {
    if (status === 'NOT_COMPLETED') {
      whereClause.status = null;
    } else {
      whereClause.status = {
        status: {
          equals: status.toUpperCase(),
        },
      };
    }
  }

  const [classes, total] = await Promise.all([
    prisma.classes.findMany({
      skip,
      take: limit,
      where: whereClause,
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        ClassAbout: true,
        status: {
          select: { status: true },
        },
      },
    }),
    prisma.classes.count({
      where: whereClause,
    }),
  ]);

  const classIds = classes.map((cls) => cls.id);

  const coinsMap = await prisma.uestCoins.findMany({
    where: {
      modelType: 'CLASS',
      modelId: { in: classIds },
    },
  });

  // Map classId => coins for quick lookup
  const coinsByClassId = new Map(
    coinsMap.map((coin) => [coin.modelId, coin.coins])
  );

  const formatted = classes.map((cls: any) => ({
    ...cls,
    coins: coinsByClassId.get(cls.id) ?? 0,
    status: cls.status && cls.status.status ? cls.status.status.toLowerCase() : 'profile not completed',
  }));

  return {
    classes: formatted,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
  };
};

export const fetchAllClassesforCount = async () => {
  const approvedCount = await prisma.classes.count({
    where: { status: { status: 'APPROVED' } },
  });

  const rejectedCount = await prisma.classes.count({
    where: { status: { status: 'REJECTED' } },
  });

  const pendingCount = await prisma.classes.count({
    where: { status: { status: 'PENDING' } },
  });

  const profileNotCompletedCount = await prisma.classes.count({
    where: { status: null },
  });

  const total = approvedCount + rejectedCount + pendingCount + profileNotCompletedCount;

  return {
    approved: approvedCount,
    rejected: rejectedCount,
    pending: pendingCount,
    PROFILE_NOT_COMPLETED: profileNotCompletedCount,
    total,
  };
};

export const updateStatusClass = async (classId: string, status: ClassApprovalStatus) => {
  const statusUpdate = await prisma.classesStatus.upsert({
    where: { classId: classId },
    update: { status },
    create: { classId, status },
  });


  return { message: 'Status updated', status: statusUpdate };
};

export const deleteClassService = async (classId: string) => {
  const classExists = await prisma.classes.findUnique({ where: { id: classId } });
  if (!classExists) {
    throw new Error('Class not found');
  }

  await prisma.$transaction(async (tx) => {
    await Promise.all([
      tx.classesStatus.deleteMany({ where: { classId } }),
      tx.classesAbout.deleteMany({ where: { classId } }),
      tx.classesEducation.deleteMany({ where: { classId } }),
      tx.classesExpereince.deleteMany({ where: { classId } }),
      tx.classesCertificates.deleteMany({ where: { classId } }),
      tx.tuitionClass.deleteMany({ where: { classId } }),
      tx.uestCoins.deleteMany({ where: { modelId: classId, modelType: 'CLASS' } }),
      tx.uestCoinTransaction.deleteMany({ where: { modelId: classId, modelType: 'CLASS' } }),
      tx.examApplication.deleteMany({ where: { classId } }),
      tx.question_answer.deleteMany({ where: { userId: classId } }),
      tx.classesThought.deleteMany({ where: { classId } }),
      tx.testimonial.deleteMany({ where: { classId } }),
      tx.blog.deleteMany({ where: { classId } }),
      tx.studentWishlist.deleteMany({ where: { savedClassId: classId } }),
      tx.classesReviews.deleteMany({ where: { classId } }),
      tx.classesCanApplyForQuestionBank.deleteMany({ where: { classId } }),
    ]);

    await tx.classes.delete({ where: { id: classId } });
  });
};

interface FilterOptions {
  page?: number;
  limit?: number;
  firstName?: string;
  lastName?: string;
  className?: string;
  boardType?: string;
  medium?: string;
  section?: string;
  subject?: string;
  coachingType?: string;
  education?: string;
  details?: string;
  sortByRating?: boolean;
  sortByReviewCount?: boolean;
}

export const getCategoryCounts = async () => {
  const categories = [
    'Education',
    'Drama',
    'Music',
    'Art & Craft',
    'Sports',
    'Languages',
    'Technology',
    'Arts',
  ];

  const classes = await prisma.classes.findMany({
    where: {
      status: {
        status: 'APPROVED',
      },
    },
    include: {
      tuitionClasses: true,
    },
  });

  const categoryCounts: Record<string, number> = {};
  categories.forEach((category) => {
    categoryCounts[category] = 0;
  });

  const classesPerCategory: Record<string, Set<string>> = {};
  categories.forEach((category) => {
    classesPerCategory[category] = new Set();
  });

  classes.forEach((cls) => {
    cls.tuitionClasses.forEach((tuition) => {
      const category = tuition.education;
      if (categories.includes(category)) {
        classesPerCategory[category].add(cls.id);
      }
    });
  });

  categories.forEach((category) => {
    categoryCounts[category] = classesPerCategory[category].size;
  });

  return categoryCounts;
};

export const getApprovedTutors = async (filters: FilterOptions) => {
  const {
    page = 1,
    limit = 10,
    firstName,
    lastName,
    className,
    boardType,
    medium,
    section,
    subject,
    coachingType,
    education,
    details,
    sortByRating = false,
    sortByReviewCount = false,
  } = filters;

  const skip = (page - 1) * limit;

  const whereClause: Prisma.ClassesWhereInput = {
    status: {
      status: 'APPROVED',
    },
    ...(firstName && {
      firstName: {
        contains: firstName.trim(),
        mode: 'insensitive' as const,
      },
    }),
    ...(lastName && {
      lastName: {
        contains: lastName.trim(),
        mode: 'insensitive' as const,
      },
    }),
    ...(className && {
      className: {
        contains: className.trim(),
        mode: 'insensitive' as const,
      },
    }),
    ...(boardType || medium || section || subject || coachingType || education || details
      ? {
          tuitionClasses: {
            some: {
              ...(education && {
                education: {
                  contains: education.trim(),
                  mode: 'insensitive',
                },
              }),
              ...(boardType && {
                boardType: {
                  contains: `"${boardType.trim()}"`,
                  mode: 'insensitive',
                },
              }),
              ...(medium && {
                medium: {
                  contains: `"${medium.trim()}"`,
                  mode: 'insensitive',
                },
              }),
              ...(section && {
                section: {
                  contains: `"${section.trim()}"`,
                  mode: 'insensitive',
                },
              }),
              ...(subject && {
                subject: {
                  contains: `"${subject.trim()}"`,
                  mode: 'insensitive',
                },
              }),
              ...(coachingType && {
                coachingType: {
                  contains: `"${coachingType.trim()}"`,
                  mode: 'insensitive',
                },
              }),
              ...(details && {
                details: {
                  contains: `"${details.trim()}"`,
                  mode: 'insensitive',
                },
              }),
            },
          },
        }
      : {}),
  };

  const tutors = await prisma.classes.findMany({
    omit: {
      email: true,
      contactNo: true,
    },
    where: whereClause,
    include: {
      ClassAbout: true,
      tuitionClasses: true,
      education: {
      select: {
        id: true,
        degree: true,
        isDegree: true,
        status: true,
      },
    },
    },
  });

  const total = await prisma.classes.count({ where: whereClause });

  const tutorsWithMeta = await Promise.all(
    tutors.map(async (tutor) => {
      const [averageRating, reviewCount] = await Promise.all([
        getAverageRatingByClassId(tutor.id),
        getReviewCountByClassId(tutor.id),
      ]);

      const hasDegree = tutor.education?.some((edu) => edu.degree !== null) ?? false;

      return {
        ...tutor,
        averageRating,
        reviewCount,
        hasDegree,
      };
    })
  );

  // Sorting logic
  tutorsWithMeta.sort((a, b) => {
    if (a.hasDegree !== b.hasDegree) {
      return Number(b.hasDegree) - Number(a.hasDegree);
    }

    // Then by reviewCount if specified
    if (sortByReviewCount && b.reviewCount !== a.reviewCount) {
      return b.reviewCount - a.reviewCount;
    }

    // Then by averageRating if specified
    if (sortByRating && b.averageRating !== a.averageRating) {
      return b.averageRating - a.averageRating;
    }

    return 0;
  });

  const paginatedTutors = tutorsWithMeta.slice(skip, skip + limit);

  return {
    data: paginatedTutors,
    total,
    page,
    totalClasses: tutorsWithMeta.length,
    totalPages: Math.ceil(total / limit),
  };
};

export const updateClassByAdminService = async (classId: string, updateData: any) => {
  try {
    if (updateData.email) {
      const existingClass = await prisma.classes.findFirst({
        where: {
          email: updateData.email,
          id: {
            not: classId,
          },
        },
      });

      if (existingClass) {
        throw new Error('Email already exists');
      }
    }

    if (updateData.username) {
      const existingClass = await prisma.classes.findFirst({
        where: {
          username: updateData.username,
          id: {
            not: classId,
          },
        },
      });

      if (existingClass) {
        throw new Error('Username already exists');
      }
    }

    await prisma.classes.update({
      where: { id: classId },
      data: {
        username: updateData.username,
        firstName: updateData.firstName,
        lastName: updateData.lastName,
        email: updateData.email,
        contactNo: updateData.contactNo,
        className: updateData.className,
        isVerified: updateData.isVerified,
      },
    });

    if (updateData.ClassAbout) {
      await prisma.classesAbout.upsert({
        where: { classId },
        update: {
          birthDate: updateData.ClassAbout.birthDate ? new Date(updateData.ClassAbout.birthDate) : undefined,
          catchyHeadline: updateData.ClassAbout.catchyHeadline,
          tutorBio: updateData.ClassAbout.tutorBio,
          profilePhoto: updateData.ClassAbout.profilePhoto,
          classesLogo: updateData.ClassAbout.classesLogo,
          videoUrl: updateData.ClassAbout.videoUrl,
        },
        create: {
          classId,
          birthDate: updateData.ClassAbout.birthDate ? new Date(updateData.ClassAbout.birthDate) : new Date(),
          catchyHeadline: updateData.ClassAbout.catchyHeadline || '',
          tutorBio: updateData.ClassAbout.tutorBio || '',
          profilePhoto: updateData.ClassAbout.profilePhoto || '',
          classesLogo: updateData.ClassAbout.classesLogo || '',
          videoUrl: updateData.ClassAbout.videoUrl || '',
        },
      });
    }

    if (updateData.education && Array.isArray(updateData.education)) {
      for (let i = 0; i < updateData.education.length; i++) {
        const edu = updateData.education[i];
        if (edu.id) {
          try {
            await prisma.classesEducation.update({
              where: { id: edu.id },
              data: {
                degree: edu.degree || '',
                university: edu.university || '',
                passoutYear: edu.passoutYear || '',
                degreeType: edu.degreeType || '',
                isDegree: edu.isDegree || false,
              },
            });
          } catch (error: any) {
            // If record doesn't exist, skip it (it was likely deleted)
            if (error.code === 'P2025') {
              console.log(`Education record with id ${edu.id} not found, skipping update`);
              continue;
            }
            throw error;
          }
        }
      }
    }

    if (updateData.experience && Array.isArray(updateData.experience)) {
      for (let i = 0; i < updateData.experience.length; i++) {
        const exp = updateData.experience[i];
        if (exp.id) {
          try {
            await prisma.classesExpereince.update({
              where: { id: exp.id },
              data: {
                title: exp.title || '',
                from: exp.from || '',
                to: exp.to || '',
                isExperience: exp.isExperience || false,
              },
            });
          } catch (error: any) {
            if (error.code === 'P2025') {
              console.log(`Experience record with id ${exp.id} not found, skipping update`);
              continue;
            }
            throw error;
          }
        }
      }
    }

    if (updateData.certificates && Array.isArray(updateData.certificates)) {
      for (let i = 0; i < updateData.certificates.length; i++) {
        const cert = updateData.certificates[i];
        if (cert.id) {
          try {
            await prisma.classesCertificates.update({
              where: { id: cert.id },
              data: {
                title: cert.title || '',
                isCertificate: cert.isCertificate || false,
                certificateUrl: cert.certificateUrl || '',
              },
            });
          } catch (error: any) {
            // If record doesn't exist, skip it (it was likely deleted)
            if (error.code === 'P2025') {
              console.log(`Certificate record with id ${cert.id} not found, skipping update`);
              continue;
            }
            throw error;
          }
        }
      }
    }

    if (updateData.tuitionClasses && Array.isArray(updateData.tuitionClasses)) {
      for (let i = 0; i < updateData.tuitionClasses.length; i++) {
        const tution = updateData.tuitionClasses[i];
        if (tution.id) {
          try {
            await prisma.tuitionClass.update({
              where: { id: tution.id },
              data: {
                education: tution.education || '',
                coachingType: tution.coachingType || '',
                boardType: tution.boardType || '',
                medium: tution.medium || '',
                section: tution.section || '',
                subject: tution.subject || '',
                details: tution.details || '',
              },
            });
          } catch (error: any) {
            // If record doesn't exist, skip it (it was likely deleted)
            if (error.code === 'P2025') {
              console.log(`Tuition class record with id ${tution.id} not found, skipping update`);
              continue;
            }
            throw error;
          }
        }
      }
    }

    const finalUpdatedClass = await prisma.classes.findUnique({
      where: { id: classId },
      include: {
        ClassAbout: true,
        status: true,
        tuitionClasses: true,
        education: true,
        experience: true,
        certificates: true,
      },
    });

    return finalUpdatedClass;
  } catch (error: any) {
    throw new Error(error.message || 'Failed to update class');
  }
};

export const getNearbyClasses = async (
  userLat: number,
  userLng: number,
  radius: number // in meters
) => {
  const classesWithAddress = await prisma.classes.findMany({
    include: {
      address: true,
    },
  });

  // Haversine formula
  function haversine(lat1: number, lon1: number, lat2: number, lon2: number) {
    const toRad = (x: number) => (x * Math.PI) / 180;
    const R = 6371000; // meters
    const dLat = toRad(lat2 - lat1);
    const dLon = toRad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(toRad(lat1)) *
        Math.cos(toRad(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  // Filter and map results
  const filtered = classesWithAddress
    .filter((cls) => cls.address && cls.address.latitude != null && cls.address.longitude != null)
    .map((cls) => {
      const addr = cls.address!;
      const distance = haversine(userLat, userLng, addr.latitude, addr.longitude);
      return {
        id: cls.id,
        firstName: cls.firstName,
        lastName: cls.lastName,
        className: cls.className,
        addressId: addr.id,
        fullAddress: addr.fullAddress,
        city: addr.city,
        state: addr.state,
        postcode: addr.postcode,
        country: addr.country,
        latitude: addr.latitude,
        longitude: addr.longitude,
        distance,
      };
    })
    .filter((item) => item.distance <= radius)
    .sort((a, b) => a.distance - b.distance);

  return filtered;
};