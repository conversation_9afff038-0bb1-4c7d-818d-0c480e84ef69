import { z } from 'zod';

export const createClassAboutSchema = z.object({
  username: z.string().min(2, 'Username must be at least 2 characters'),
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  className: z.string().min(2, 'Class name must be at least 2 characters'),
  email: z.string().email('Invalid email format'),
  birthDate: z.string().transform((str) => new Date(str)),
  contactNo: z.string().min(10, 'Contact number must be valid'),
  catchyHeadline: z.string().optional(),
  tutorBio: z.string().optional(),
  profilePhoto: z.string().optional(),
  schoolLogo: z.string().optional(),
  videoUrl: z.string().optional(),
});

export const deleteRecordSchema = z
  .object({
    classId: z.string().min(1, 'Class ID is required'),
  })
  .strict();

export const updateStatusSchema = z
  .object({
    status: z.enum(['PENDING', 'APPROVED', 'REJECTED'], {
      errorMap: () => ({ message: 'Status must be PENDING, APPROVED, or REJECTED' }),
    }),
  })
  .strict();


