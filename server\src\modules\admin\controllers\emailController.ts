import prisma from '@/config/prismaClient';
import { Request, Response } from 'express';
import { transporter } from '@/utils/email';
import { createNotificationTemplate } from '@/utils/emailTemplates';
import dotenv from 'dotenv';
dotenv.config();

export const sendMailHandler = async (req: Request, res: Response) => {
  const { email, subject, message, status } = req.body;

  try {
    let classesList: (string | null)[] = [];
    if (email) {
      // Send to a specific email
      classesList = [email];
    } else if (status) {
      const classesWithStatus = await prisma.classes.findMany({
        where: {
          status: {
            status: status,
          },
        },
        select: {
          email: true,
        },
      });
      classesList = classesWithStatus.map((cls) => cls.email);
    } else {
      // Send to all classes
      const classes = await prisma.classes.findMany({
        select: {
          email: true,
        },
      });
      classesList = classes.map((cls) => cls.email);
    }

    const htmlContent = createNotificationTemplate(subject, message);

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: classesList.join(','),
      subject,
      text: message, 
      html: htmlContent,
    };

    await transporter.sendMail(mailOptions);
    res.status(200).json({ success: true, message: 'Email sent successfully!' });
  } catch (error) {
    console.log('Error sending email:', error);
    res.status(500).json({ success: false, message: 'Failed to send email.' });
  }
};
