import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const seedConstants = async () => {
  try {
    console.log('🌱 Starting seed process...');

    // Create main TuitionClasses category first
    const tuitionClassesCategory = await prisma.constantCategory.upsert({
      where: { name: 'TuitionClasses' },
      update: {},
      create: {
        name: 'TuitionClasses'
      },
    });

    console.log('✅ TuitionClasses category created');

    // Education category with sub-categories (special case)
    const educationData = {
      name: 'Education',
      subDetails: [
        {
          name: 'Medium',
          values: ['Gujarati', 'English', 'Hindi']
        },
        {
          name: 'Subject',
          values: ['Mathematics', 'Science', 'English', 'Social Studies', 'Gujarati', 'Hindi', 'Physics', 'Chemistry', 'Biology', 'Computer Science']
        },
        {
          name: 'Section',
          values: ['Pre-school', 'Pre-primary', 'Primary', 'Secondary', 'Higher Secondary', 'Undergraduate', 'Postgraduate', 'Vocational', 'Diploma']
        },
        {
          name: 'Board Type',
          values: ['Gujarat', 'CBSE', 'ICSE', 'IB', 'Cambridge']
        },
        {
          name: 'Degree',
          values: ['B.Sc Mathematics', 'B.Sc Computer Science', 'B.Com', 'BBA', 'BCA', 'BA', 'M.Sc', 'M.Com', 'MBA', 'MCA', 'MA', 'Ph.D', 'B.Tech', 'M.Tech']
        }
      ]
    };

    // Other categories with direct values (simple case)
    const otherCategoriesData = [
      { name: 'Music', values: ['Piano', 'Guitar', 'Violin', 'Drums', 'Sitar', 'Tabla', 'Vocal Training', 'Flute'] },
      { name: 'Dance', values: ['Classical', 'Hip Hop', 'Bollywood', 'Bharatanatyam', 'Kathak', 'Contemporary', 'Garba', 'Folk Dance'] },
      { name: 'Sports', values: ['Cricket', 'Football', 'Basketball', 'Tennis', 'Badminton', 'Swimming', 'Hockey'] },
      { name: 'Technology', values: ['Python', 'JavaScript', 'Java', 'Web Development', 'Mobile Development', 'Data Science', 'Machine Learning'] },
      { name: 'Drama', values: ['Theater', 'Stage Performance', 'Comedy', 'Tragedy', 'Improv', 'Musical Theater', 'Method Acting', 'Scriptwriting', 'Directing'] },
      { name: 'Art & Craft', values: ['Painting', 'Sculpting', 'Pottery', 'Sketching', 'Drawing', 'Crafts', 'Origami'] },
      { name: 'Foreign Languages', values: ['French', 'Spanish', 'German', 'Mandarin', 'Japanese', 'Korean', 'Italian', 'Russian', 'Arabic'] },
      { name: 'Computer Classes', values: ['Basic Computer', 'MS Office', 'Programming', 'Web Design', 'Database Management', 'Computer Hardware', 'Software Installation', 'Internet & Email'] },
      { name: 'Cooking Classes', values: ['Indian Cuisine', 'Continental Cuisine', 'Chinese Cuisine', 'Baking', 'Pastry Making', 'Gujarati Cuisine', 'South Indian Cuisine', 'Italian Cuisine', 'Desserts & Sweets'] },
      { name: 'Garba Classes', values: ['Traditional Garba', 'Modern Garba', 'Dandiya Raas', 'Folk Garba', 'Competition Garba', 'Kids Garba', 'Couple Garba'] },
      { name: 'Vaidik Maths', values: ['Basic Calculations', 'Speed Mathematics', 'Mental Math', 'Vedic Multiplication', 'Vedic Division', 'Square & Cube Roots', 'Percentage Calculations'] },
      { name: 'Gymnastic Classes', values: ['Artistic Gymnastics', 'Rhythmic Gymnastics', 'Floor Exercise', 'Balance Beam', 'Parallel Bars', 'Rings', 'Vault', 'Kids Gymnastics'] },
      { name: 'Yoga Classes', values: ['Hatha Yoga', 'Vinyasa Yoga', 'Ashtanga Yoga', 'Iyengar Yoga', 'Kundalini Yoga', 'Pranayama', 'Meditation', 'Power Yoga', 'Restorative Yoga'] },
      { name: 'Aviation Classes', values: ['Pilot Training', 'Aircraft Maintenance', 'Air Traffic Control', 'Aviation Management', 'Flight Dispatcher', 'Ground Handling', 'Aviation Safety', 'Commercial Pilot License'] },
      { name: 'Designing Classes', values: ['Graphic Design', 'Web Design', 'UI/UX Design', 'Interior Design', 'Fashion Design', 'Product Design', 'Logo Design', 'Digital Art', 'Animation Design'] },
    ];

    // Process Education category (special case with sub-categories)
    const educationDetail = await prisma.constantDetail.upsert({
      where: {
        name_categoryId: {
          name: educationData.name,
          categoryId: tuitionClassesCategory.id
        }
      },
      update: {},
      create: {
        name: educationData.name,
        categoryId: tuitionClassesCategory.id,
      },
    });

    for (const subDetail of educationData.subDetails) {
      const createdSubDetail = await prisma.constantSubDetail.upsert({
        where: {
          name_detailId: {
            name: subDetail.name,
            detailId: educationDetail.id
          }
        },
        update: {},
        create: {
          name: subDetail.name,
          detailId: educationDetail.id,
        },
      });

      for (const value of subDetail.values) {
        await prisma.constantSubDetailValue.upsert({
          where: {
            name_subDetailId: {
              name: value,
              subDetailId: createdSubDetail.id
            }
          },
          update: {},
          create: {
            name: value,
            subDetailId: createdSubDetail.id,
          },
        });
      }
    }

    console.log('✅ Education category with sub-details created');

    // Process other categories (direct SubDetails, no Values)
    for (const category of otherCategoriesData) {
      const createdDetail = await prisma.constantDetail.upsert({
        where: {
          name_categoryId: {
            name: category.name,
            categoryId: tuitionClassesCategory.id
          }
        },
        update: {},
        create: {
          name: category.name,
          categoryId: tuitionClassesCategory.id,
        },
      });

      // Create SubDetails directly (no Values under them)
      for (const value of category.values) {
        await prisma.constantSubDetail.upsert({
          where: {
            name_detailId: {
              name: value,
              detailId: createdDetail.id
            }
          },
          update: {},
          create: {
            name: value,
            detailId: createdDetail.id,
          },
        });
      }
    }

    console.log('✅ Other categories with direct values created');
    console.log('🎉 Seeding completed successfully!');
    
  } catch (e) {
    console.error('❌ Seeding failed:', e);
    throw e;
  } finally {
    await prisma.$disconnect();
  }
};

seedConstants();
