import { z } from 'zod';

export const registerSchema = z.object({
  firstName: z.string().min(1, 'First name is required').regex(/^[a-zA-Z]+$/, 'Invalid first name'),
  lastName: z.string().min(1, 'Last name is required').regex(/^[a-zA-Z]+$/, 'Invalid last name'),
  contactNo: z.string().regex(/^\d{10}$/, 'Invalid mobile number. It must be exactly a 10-digit number with digits from 0 to 9.'),
  referralCode: z.string().optional(),
});

export const loginSchema = z.object({
  contactNo: z.string().regex(/^\d{10}$/, 'Invalid mobile number. It must be a 10-digit number starting with 7, 8, or 9.'),
  email: z.string().email('Invalid email address').optional(),
});

export const verifyOtpSchema = z.object({
  contactNo: z.string().regex(/^\d{10}$/, 'Invalid mobile number. It must be a 10-digit number starting with 7, 8, or 9.'),
  otp: z.string().regex(/^\d{6}$/, 'Invalid OTP. It must be a 6-digit number.'),
  email: z.string().email('Invalid email address').optional(),
  firstName: z.string().min(1, 'First name is required').regex(/^[a-zA-Z]+$/, 'Invalid first name').optional(),
  lastName: z.string().min(1, 'Last name is required').regex(/^[a-zA-Z]+$/, 'Invalid last name').optional(),
});

export const resendOtpSchema = z.object({
  contactNo: z.string().regex(/^\d{10}$/, 'Invalid mobile number. It must be a 10-digit number starting with 7, 8, or 9.'),
  firstName: z.string().min(1, 'First name is required').regex(/^[a-zA-Z]+$/, 'Invalid first name').optional(),
});

export const continueWithEmailSchema = z.object({
  email: z.string().email('Invalid email address'),
});