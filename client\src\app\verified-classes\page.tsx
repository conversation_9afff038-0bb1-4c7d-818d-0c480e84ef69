"use client";

import React, { Suspense, useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { motion, Transition, Variants } from "framer-motion";
import {
  <PERSON>,
  CardHeader,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Star, ChevronLeft, ChevronRight, Filter, X } from "lucide-react";
import { axiosInstance } from "@/lib/axios";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import { toast } from "sonner";
import Image from "next/image";
import { Skeleton } from "@/components/ui/skeleton";
import { FilterInput } from "./FilterInput";
import { parseAndJoinArray } from "@/lib/helper";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON><PERSON>ontent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { IoShieldCheckmark } from "react-icons/io5";
import { FaGraduationCap } from "react-icons/fa";

interface Constant {
  id: string;
  name: string;
  details: { id: string; value: string }[];
}

interface Education {
  isDegree: boolean;
  status: string;
  degree: string;
}

interface TuitionClass {
  education: string;
  coachingType: string;
  boardType?: string;
  medium?: string;
  section?: string;
  subject?: string;
  details?: string;
  pricingPerMonth: number;
  pricingPerCourse: number;
  timeSlots: { id: string; from: string; to: string }[];
}

export interface Tutor {
  id: string;
  firstName: string;
  lastName: string;
  className: string;
  ClassAbout: {
    tutorBio: string;
    classesLogo: string;
  };
  tuitionClasses: TuitionClass[];
  averageRating?: number;
  reviewCount?: number;
  isVerified: boolean;
  education: Education[];
  distance?: number;
}

const TutorListContent = () => {
  const router = useRouter();
  const [teachers, setTeachers] = useState<Tutor[]>([]);
  const [constants, setConstants] = useState<Constant[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [isStudentLoggedIn] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const searchParams = useSearchParams();
  const [useNearby, setUseNearby] = useState(false);
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [distance, setDistance] = useState(1000); // default 1km
  const [locationError, setLocationError] = useState<string | null>(null);

  const [filters, setFilters] = useState({
    education: searchParams.get("education") || "",
    details: searchParams.get("details") || "",
    boardType: searchParams.get("boardType") || "",
    medium: searchParams.get("medium") || "",
    section: searchParams.get("section") || "",
    coachingType: searchParams.get("coachingType") || "",
    subject: searchParams.get("subject") || "",
    firstName: searchParams.get("firstName") || "",
    lastName: searchParams.get("lastName") || "",
    className: searchParams.get("className") || "",
    sortByRating: true,
    sortByReviewCount: true,
  });

  const getConstantValues = (name: string) => {
    return constants.find((cat) => cat.name === name)?.details || [];
  };

  const fetchConstants = async () => {
    try {
      const res = await axiosInstance.get("/constant");
      setConstants(res.data);
    } catch {
      toast.error('Failed to fetch filters');
    }
  };

  const getLocation = () => {
    if (!navigator.geolocation) {
      setLocationError('Geolocation is not supported by your browser.');
      return;
    }
    navigator.geolocation.getCurrentPosition(
      (pos) => {
        setUserLocation({ lat: pos.coords.latitude, lng: pos.coords.longitude });
        setLocationError(null);
      },
      () => {
        setLocationError('Unable to retrieve your location.');
      }
    );
  };

  const fetchNearbyTutors = async () => {
    if (!userLocation) return;
    setLoading(true);
    try {
      const res = await axiosInstance.get('/classes/nearby', {
        params: {
          lat: userLocation.lat,
          lng: userLocation.lng,
          radius: distance,
        },
      });
      setTeachers(res.data.data || []);
      setTotalPages(1);
    } catch {
      toast.error('Failed to fetch nearby classes');
    } finally {
      setLoading(false);
    }
  };

  const fetchTutors = async () => {
    setLoading(true);
    try {
      const res = await axiosInstance.get("/classes/approved-tutors", {
        params: {
          page,
          limit: 9,
          ...filters,
        },
      });
      const tutors = res.data.data;
      setTeachers(tutors);
      setTotalPages(res.data.totalPages);
    } catch {
      toast.error("Failed to fetch tutors");
    } finally {
      setLoading(false);
    }
  };

  const resetFilters = () => {
    setFilters({
      education: "",
      details: "",
      boardType: "",
      medium: "",
      section: "",
      coachingType: "",
      subject: "",
      firstName: "",
      lastName: "",
      className: "",
      sortByRating: true, // Always sort by rating
      sortByReviewCount: true, // Always sort by review count
    });
    setPage(1);
    fetchTutors();
  };

  useEffect(() => {
    fetchConstants();
  }, []);

  useEffect(() => {
    if (useNearby && userLocation) {
      fetchNearbyTutors();
    } else {
      fetchTutors();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, useNearby, userLocation, distance]);
  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      } as Transition,
    },
  };

  const itemVariants: Variants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring' as const,
        stiffness: 100,
      },
    },
  };

  const FilterSelect = ({
    label,
    value,
    onChange,
    options,
  }: {
    label: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
    options: { id: string; value: string }[];
  }) => (
    <div className="flex flex-col">
      <label className="text-sm font-medium text-muted-foreground mb-1">
        {label}
      </label>
      <select
        className="border rounded-lg px-3 py-2 text-sm
                   dark:bg-black
                   text-gray-900 dark:text-white"
        value={value}
        onChange={onChange}
      >
        <option value="" className="bg-white dark:bg-zinc-900">
          All {label}
        </option>
        {options.map((item) => (
          <option
            key={item.id}
            value={item.value}
            className="bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100"
          >
            {item.value}
          </option>
        ))}
      </select>
    </div>
  );

  // Dynamic options for Details based on selected Education
  const selectedEducation = filters.education;
  const detailsOptions = selectedEducation
    ? getConstantValues(selectedEducation)
    : [];

  useEffect(() => {
  if (useNearby && !userLocation) {
    getLocation();
  }
}, [useNearby]);

  return (
    <motion.section
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="container mx-auto px-4 sm:px-6 lg:px-8 py-20"
    >
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-12"
      >
        <h1 className="text-4xl font-bold">Find Your Perfect Tutor</h1>
        <p className="mt-4 text-lg font-medium bg-gradient-to-r from-gray-700 to-gray-500 dark:from-gray-300 dark:to-gray-400 bg-clip-text text-transparent">
          Discover experienced tutors who can help you achieve your learning
          goals
        </p>
      </motion.div>

      <motion.div
        className="mb-8 bg-white/30 dark:bg-black/30 backdrop-blur-lg rounded-xl p-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-2">
            <Filter className="w-5 h-5 text-customOrange" />
            <h3 className="text-xl font-semibold">Filters</h3>
          </div>
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              className="hover:bg-customOrange/10"
              onClick={() => setIsFilterOpen(!isFilterOpen)}
            >
              {isFilterOpen ? (
                <X className="w-4 h-4" />
              ) : (
                <Filter className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>

        <div
          className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 transition-all duration-300 ${
            isFilterOpen ? "block" : "hidden"
          }`}
        >
          <FilterSelect
            label="Category"
            value={filters.education}
            onChange={(e) => {
              setFilters((prev) => ({
                ...prev,
                education: e.target.value,
                details: "",
              }));
            }}
            options={constants
              .filter((cat) =>
                [
                  "Education",
                  "Drama",
                  "Music",
                  "Art & Craft",
                  "Sports",
                  "Languages",
                  "Technology",
                  "Dance",
                  "Computer Classes",
                  "Cooking Classes",
                  "Garba Classes",
                  "Vaidik Maths",
                  "Gymnastic Classes",
                  "Yoga Classes",
                  "Aviation Classes",
                  "Designing Classes",
                ].includes(cat.name)
              )
              .map((cat) => ({ id: cat.id, value: cat.name }))}
          />
          {filters.education && filters.education !== "Education" && (
            <FilterSelect
              label="Details"
              value={filters.details}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, details: e.target.value }))
              }
              options={detailsOptions}
            />
          )}
          {(!filters.education || filters.education === "Education") && (
            <>
              <FilterSelect
                label="Board Type"
                value={filters.boardType}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, boardType: e.target.value }))
                }
                options={getConstantValues("Board Type")}
              />
              <FilterSelect
                label="Medium"
                value={filters.medium}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, medium: e.target.value }))
                }
                options={getConstantValues("Medium")}
              />
              <FilterSelect
                label="Section"
                value={filters.section}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, section: e.target.value }))
                }
                options={getConstantValues("Section")}
              />
              <FilterSelect
                label="Subject"
                value={filters.subject}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, subject: e.target.value }))
                }
                options={getConstantValues("Subject")}
              />
            </>
          )}
          <FilterSelect
            label="Coaching Type"
            value={filters.coachingType}
            onChange={(e) =>
              setFilters((prev) => ({ ...prev, coachingType: e.target.value }))
            }
            options={getConstantValues("Coaching Type")}
          />
          <FilterInput
            label="First Name"
            value={filters.firstName}
            onChange={(e) =>
              setFilters((prev) => ({ ...prev, firstName: e.target.value }))
            }
          />
          <FilterInput
            label="Class Name"
            value={filters.className}
            onChange={(e) =>
              setFilters((prev) => ({ ...prev, className: e.target.value }))
            }
          />
        </div>
        <div className={`flex gap-4 mt-4 ${isFilterOpen ? "block" : "hidden"}`}>
          <Button
            className="w-[200px] bg-customOrange hover:bg-customOrange/90"
            onClick={() => {
              setPage(1);
              fetchTutors();
            }}
          >
            Apply Filters
          </Button>
          <Button
            variant="default"
            className="w-[200px]"
            onClick={resetFilters}
          >
            Reset
          </Button>
        </div>
      </motion.div>
      <div className="flex flex-nowrap items-center gap-6 w-full mb-5">
        <label className="flex items-center gap-2 mb-0 whitespace-nowrap flex-shrink-0">
          <input
            type="checkbox"
            checked={useNearby}
            onChange={() => setUseNearby((v) => !v)}
            className="accent-customOrange"
          />
          <span>Show Nearby Classes</span>
        </label>
        {useNearby && (
          <>
            <div className="flex items-center gap-2 flex-shrink-0">
              <select
                className="border rounded px-2 py-1 min-w-[90px]"
                value={distance}
                onChange={(e) => setDistance(Number(e.target.value))}
              >
                <option value={100}>100m</option>
                <option value={500}>500m</option>
                <option value={1000}>1km</option>
                <option value={5000}>5km</option>
                <option value={10000}>10km</option>
                <option value={10000}>60km</option>
              </select>
              <span className="whitespace-nowrap">
                {distance >= 1000
                  ? `${distance / 1000}km Radius`
                  : `${distance}m Radius`}
              </span>
            </div>
            {locationError && <span className="text-red-500 ml-2 flex-shrink-0">{locationError}</span>}
          </>
        )}
      </div>
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Skeleton key={i} className="h-96 w-full rounded-xl" />
          ))}
        </div>
      ) : teachers.length === 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-10"
        >
          <p className="text-muted-foreground">
            No tutors found. Try adjusting your filters.
          </p>
        </motion.div>
      ) : (
        <>
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {teachers.map((teacher, i) => (
              <motion.div
                key={i}
                variants={itemVariants}
                whileHover={{ y: -5 }}
                className="h-full"
              >
                <Card className="h-full bg-white/50 dark:bg-black/50 backdrop-blur-sm transition-all duration-300">
                  <CardHeader className="flex flex-row items-center gap-4">
                    <motion.div
                      className="relative w-30 h-30 rounded-full overflow-hidden ring-2 ring-customOrange/20"
                      whileHover={{ scale: 1.05 }}
                    >
                      <Image
                        src={
                          teacher.ClassAbout && teacher.ClassAbout.classesLogo
                            ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${teacher.ClassAbout.classesLogo}`
                            : "/default-profile.jpg"
                        }
                        alt={teacher.firstName}
                        fill
                        className="object-cover"
                      />
                    </motion.div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold hover:text-customOrange transition-colors">
                        {teacher.firstName} {teacher.lastName}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {teacher.className}
                      </p>
                    </div>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="relative group cursor-pointer">
                          <IoShieldCheckmark className="text-green-500" />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent className="text-xs">
                        Verified by Uest
                      </TooltipContent>
                    </Tooltip>
                  </CardHeader>
                  <CardContent className="flex-1 space-y-4">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        {teacher.education?.filter(
                          (edu) => edu.isDegree && edu.status === "APPROVED"
                        ).length > 0 && (
                          <div className="text-xs text-muted-foreground mt-1 flex items-center gap-1 flex-wrap">
                            <FaGraduationCap className="text-customOrange text-lg" />
                            <span className="text-md">
                              Degrees:{" "}
                              {teacher.education
                                .filter(
                                  (edu) =>
                                    edu.isDegree && edu.status === "APPROVED"
                                )
                                .map((edu) => edu.degree)
                                .join(", ")}
                            </span>
                          </div>
                        )}
                      </TooltipTrigger>
                      <TooltipContent className="text-xs">
                        Verified by Uest
                      </TooltipContent>
                    </Tooltip>

                    <p className="line-clamp-2 text-sm text-muted-foreground">
                      {(teacher.ClassAbout && teacher.ClassAbout.tutorBio) ||
                        "No bio available."}
                    </p>
                    {Array.isArray(teacher.tuitionClasses) && teacher.tuitionClasses.length > 0 && (
                      <div className="space-y-2 p-4 rounded-lg bg-black/5 dark:bg-white/5">
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div className="space-y-1">
                            <p className="font-medium">Category</p>
                            <p className="text-muted-foreground">
                              {parseAndJoinArray(
                                teacher.tuitionClasses[0].education
                              ) || "N/A"}
                            </p>
                          </div>
                          <div className="space-y-1">
                            <p className="font-medium">Coaching Type</p>
                            <p className="text-muted-foreground">
                              {parseAndJoinArray(
                                teacher.tuitionClasses[0].coachingType
                              ) || "N/A"}
                            </p>
                          </div>
                          {teacher.tuitionClasses[0].education ===
                            "Education" && (
                            <>
                              <div className="space-y-1">
                                <p className="font-medium">Board</p>
                                <p className="text-muted-foreground">
                                  {parseAndJoinArray(
                                    teacher.tuitionClasses[0].boardType
                                  ) || "N/A"}
                                </p>
                              </div>
                              <div className="space-y-1">
                                <p className="font-medium">Medium</p>
                                <p className="text-muted-foreground">
                                  {parseAndJoinArray(
                                    teacher.tuitionClasses[0].medium
                                  ) || "N/A"}
                                </p>
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    )}
                    {teacher.distance !== undefined && (
                      <div className="text-sm text-customOrange font-semibold">
                        Distance: {(teacher.distance / 1000).toFixed(2)} km
                      </div>
                    )}
                  </CardContent>
                  <CardFooter className="flex flex-col items-start gap-4">
                    <div className="flex items-center gap-1 pt-2">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-semibold text-foreground">
                        {teacher.averageRating
                          ? teacher.averageRating.toFixed(1)
                          : "0"}
                      </span>
                      <span>({teacher.reviewCount || 0} reviews)</span>
                    </div>
                    <div className="flex gap-2 w-full">
                      <Button
                        className="flex-1 bg-customOrange hover:bg-[#E88143]"
                        onClick={() =>
                          router.push(`/classes-details/${teacher.id}`)
                        }
                      >
                        View Profile
                      </Button>
                      <Button
                        variant="outline"
                        className="flex-1 hover:bg-orange-50"
                        onClick={() => {
                          if (!isStudentLoggedIn) {
                            setShowLoginModal(true);
                            return;
                          }
                          const userName = `${teacher.firstName} ${teacher.lastName}`;
                          router.push(
                            `/student/chat?userId=${
                              teacher.id
                            }&userName=${encodeURIComponent(userName)}`
                          );
                        }}
                      >
                        Message
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          <div className="flex justify-center items-center mt-8 gap-4">
            <Button
              variant="outline"
              disabled={page === 1}
              onClick={() => setPage(page - 1)}
              className="flex gap-2"
            >
              <ChevronLeft className="h-4 w-4" /> Previous
            </Button>
            <span className="text-sm text-muted-foreground">
              Page {page} of {totalPages}
            </span>
            <Button
              variant="outline"
              disabled={page === totalPages}
              onClick={() => setPage(page + 1)}
              className="flex gap-2"
            >
              Next <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          <Dialog open={showLoginModal} onOpenChange={setShowLoginModal}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle className="text-center">
                  Login Required
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <p className="text-center text-muted-foreground">
                  Please login as a student to add this class to send a message.
                </p>
              </div>
            </DialogContent>
          </Dialog>
        </>
      )}
    </motion.section>
  );
};

const TutorListPage = () => {
  return (
    <div className="min-h-screen">
      <Header />
      <Suspense
        fallback={
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <Skeleton key={i} className="h-96 w-full rounded-xl" />
              ))}
            </div>
          </div>
        }
      >
        <TutorListContent />
      </Suspense>
      <Footer />
    </div>
  );
};

export default TutorListPage;