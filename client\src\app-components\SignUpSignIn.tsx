'use client';

import { useEffect, useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { FiUser } from "react-icons/fi";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import {
  loginUser,
  registerUser,
} from "@/services/AuthService";
import AuthActions from "./AuthActions";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store";
import { setUser } from "@/store/slices/userSlice";
import { Loader2 } from "lucide-react";
import CustomModal from "@/components/ui/CustomModal";

const loginSchema = z.object({
  contactNo: z.string().regex(/^91[789]\d{9}$/, 'Invalid mobile number. It must start with 91 and be a valid 10-digit Indian number.'),
});

const registerSchema = z.object({
  firstName: z.string().min(2, 'First name is required').regex(/^[a-zA-Z]+$/, 'Invalid first name'),
  lastName: z.string().min(2, 'Last name is required').regex(/^[a-zA-Z]+$/, 'Invalid last name'),
  contactNo: z.string().regex(/^91[789]\d{9}$/, 'Invalid mobile number. It must start with 91 and be a valid 10-digit Indian number.'),
  referralCode: z.string().optional(),
});

type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;

// Form error alert component
const FormErrorAlert = ({ message }: { message: string }) => {
  if (!message) return null;

  return (
    <Alert className="mb-4 border-red-500 bg-red-50 dark:bg-red-900/20">
      <AlertCircle className="h-4 w-4 text-red-500" />
      <AlertDescription className="text-red-500">{message}</AlertDescription>
    </Alert>
  );
};

const SignUpSignIn = () => {
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [isLogin, setIsLogin] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [disablebtn, setDisablebtn] = useState<boolean>(false);
  const [timeleft, setTimeleft] = useState<number>(120);
  const [authError, setAuthError] = useState<string>("");

  const dispatch = useDispatch<AppDispatch>();

  const authForm = useForm<LoginFormValues | RegisterFormValues>({
    resolver: zodResolver(isLogin ? loginSchema : registerSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      contactNo: '91',
    },
  });


  const onAuthSubmit = async (data: LoginFormValues | RegisterFormValues) => {
    setIsSubmitting(true);
    setAuthError(""); // Clear previous errors
    try {
      let response;
      if (isLogin) {       
        response = await loginUser(data as LoginFormValues);
      } else {
        response = await registerUser(data as RegisterFormValues);
      }

      if (response.success === false) {
        setAuthError(response.message || 'Authentication failed');
        return;
      }
      if (isLogin && response.data) {
        const { user } = response.data;
        if (user) {
          dispatch(setUser({ user }));
        }
        toast.success("Logged in successfully");
        setShowAuthModal(false);
        authForm.reset();
      } else {
        toast.success(response.message || "Verification email sent. Please check your inbox and verify your email.");
        setShowAuthModal(false);
        authForm.reset();
      }
    } catch (error: unknown) {
      setAuthError((error as any)?.response?.data?.message || "An error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    let interval:any;
    if (disablebtn && timeleft > 0) {
      interval = setInterval(() => {
        setTimeleft((prev) => {
          if (prev <= 1) {
            clearInterval(interval);
            setDisablebtn(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [disablebtn, timeleft]);

  useEffect(() => {
    const handleOpenClassModal = () => {
      if (localStorage.getItem('studentToken')) {
        toast.error("You are already logged in as a student. Please logout first to login as tutor.");
        window.location.href = "/";
        return;
      }
      setShowAuthModal(true);
    };
    window.addEventListener('open-class-modal', handleOpenClassModal);
    return () => {
      window.removeEventListener('open-class-modal', handleOpenClassModal);
    };
  }, []);

  const toggleAuthModal = () => {
    if (!showAuthModal && localStorage.getItem('studentToken')) {
      toast.error("You are already logged in as a student. Please logout first to login as tutor.");
      window.location.href = "/";
      return;
    }
    setShowAuthModal(!showAuthModal);
    setAuthError("");
    authForm.reset();
  };

  return (
    <>
      <button id="class-mobile-signin" data-mobile-signin-button="class" className="hidden" onClick={toggleAuthModal}></button>
        <div className="hidden sm:block">
        <AuthActions toggleModal={toggleAuthModal} />
      </div>
      <CustomModal isOpen={showAuthModal} onClose={toggleAuthModal}>
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="flex bg-muted rounded-lg p-1">
              <Button
                variant={isLogin ? 'default' : 'ghost'}
                className={`px-4 py-2 rounded-lg ${
                  isLogin
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground dark:text-white'
                }`}
                onClick={() => setIsLogin(true)}
              >
                Login
              </Button>
              <Button
                variant={!isLogin ? 'default' : 'ghost'}
                className={`px-4 py-2 rounded-lg ${
                  !isLogin
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground dark:text-white'
                }`}
                onClick={() => setIsLogin(false)}
              >
                Sign Up
              </Button>
            </div>
          </div>
          <p className="text-muted-foreground mb-2">Sign up/sign in as a classes</p>
          <h2 className="text-2xl font-bold mb-4">{isLogin ? 'Welcome Back' : 'Join Us'}</h2>
        </div>

        <Form {...authForm}>
          <form onSubmit={authForm.handleSubmit(onAuthSubmit)} className="space-y-6">
            {authError && <FormErrorAlert message={authError} />}
            {!isLogin && (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <FormField
                  control={authForm.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground dark:text-white">First Name</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <FiUser
                            className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground"
                            size={20}
                          />
                          <Input placeholder="First Name" className="pl-10" {...field} />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={authForm.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground dark:text-white">Last Name</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <FiUser
                            className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground"
                            size={20}
                          />
                          <Input placeholder="Last Name" className="pl-10" {...field} />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

      

         

            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : isLogin ? (
                'Login'
              ) : (
                'Sign Up'
              )}
            </Button>
          </form>
        </Form>
      </CustomModal>

    </>
  );
};

export default SignUpSignIn;
