'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Plus, Search, Trash2, Edit, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/app-components/dataTable';
import {
  getSubDetailById,
  getValuesBySubDetail,
  createValue,
  updateValue,
  deleteValue
} from '@/services/constantsApi';
import { ConstantSubDetail, ConstantSubDetailValue } from '@/lib/types';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Checkbox } from '@/components/ui/checkbox';



export default function SubDetailValuesPage() {
  const params = useParams();
  const router = useRouter();
  const subDetailId = params.subDetailId as string;

  const [subDetail, setSubDetail] = useState<ConstantSubDetail | null>(null);
  const [values, setValues] = useState<ConstantSubDetailValue[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [appliedSearchTerm, setAppliedSearchTerm] = useState('');

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingValue, setEditingValue] = useState<ConstantSubDetailValue | null>(null);
  const [newValueName, setNewValueName] = useState('');
  const [newValueActive, setNewValueActive] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [totalRecords, setTotalRecords] = useState(0);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingValue, setDeletingValue] = useState<ConstantSubDetailValue | null>(null);

  const fetchSubDetail = useCallback(async () => {
    try {
      const data = await getSubDetailById(subDetailId);
      setSubDetail(data);
    } catch {
      toast.error('Sub-detail not found');
    }
  }, [subDetailId]);

  const fetchValues = useCallback(async (page: number = currentPage, search?: string) => {
    try {
      setLoading(true);
      const result = await getValuesBySubDetail(subDetailId, page, pageSize, search);
      setValues(result.data);
      setTotalPages(result.pagination.totalPages);
      setTotalRecords(result.pagination.total);
    } catch {
      toast.error('Failed to fetch values');
    } finally {
      setLoading(false);
    }
  }, [subDetailId, currentPage, pageSize]);

  const createValueHandler = async () => {
    if (!newValueName.trim()) {
      toast.error('Value name is required');
      return;
    }

    try {
      await createValue({
        name: newValueName,
        subDetailId: subDetailId,
        isActive: newValueActive
      });
      toast.success('Value created successfully');
      setNewValueName('');
      setNewValueActive(true);
      setIsAddDialogOpen(false);
      fetchValues();
    } catch {
      toast.error('Failed to create value');
    }
  };

  const updateValueHandler = async () => {
    if (!editingValue || !newValueName.trim()) {
      toast.error('Value name is required');
      return;
    }

    try {
      await updateValue(editingValue.id, {
        name: newValueName,
        isActive: newValueActive
      });
      toast.success('Value updated successfully');
      setNewValueName('');
      setNewValueActive(true);
      setIsEditDialogOpen(false);
      setEditingValue(null);
      fetchValues();
    } catch {
      toast.error('Failed to update value');
    }
  };


  const handleDeleteClick = useCallback((value: ConstantSubDetailValue) => {
    setDeletingValue(value);
    setIsDeleteDialogOpen(true);
  }, []);

  const confirmDelete = useCallback(async () => {
    if (!deletingValue) return;

    try {
      await deleteValue(deletingValue.id);
      toast.success('Value deleted successfully');
      setCurrentPage(1);
      fetchValues(1);
    } catch {
      toast.error('Cannot delete value as it may be in use by other records');
    } finally {
      setIsDeleteDialogOpen(false);
      setDeletingValue(null);
    }
  }, [deletingValue, fetchValues]);





  const handleEdit = useCallback((value: ConstantSubDetailValue) => {
    setEditingValue(value);
    setNewValueName(value.name);
    setNewValueActive(value.isActive);
    setIsEditDialogOpen(true);
  }, []);



  const applySearch = useCallback(() => {
    setAppliedSearchTerm(searchTerm);
    setCurrentPage(1);
    fetchValues(1, searchTerm.trim() || undefined);
  }, [searchTerm, fetchValues]);

  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setAppliedSearchTerm('');
    setCurrentPage(1);
    fetchValues(1, undefined);
  }, [fetchValues]);

  const handleSearchKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      applySearch();
    }
  }, [applySearch]);



  const columns = useMemo<ColumnDef<ConstantSubDetailValue>[]>(() => [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('name')}</div>
      ),
    },

    {
      id: 'actions',
      header: () => (
        <div className="text-right">Actions</div>
      ),
      cell: ({ row }) => (
        <div className="flex items-center justify-end space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(row.original)}
            title="Edit Value"
            className="h-8 w-8 p-0"
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteClick(row.original)}
            title="Delete Value"
            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
  ], [handleEdit, handleDeleteClick]);


  useEffect(() => {
    fetchSubDetail();
    fetchValues(1);
  }, [fetchSubDetail]);

  useEffect(() => {
    if (appliedSearchTerm) {
      fetchValues(currentPage, appliedSearchTerm);
    } else {
      fetchValues(currentPage);
    }
  }, [currentPage, appliedSearchTerm, fetchValues]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading values...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">

      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">
              {subDetail?.detail.category.name} → {subDetail?.detail.name} → {subDetail?.name} - Values
            </h1>
            <p className="text-gray-600 mt-1">Manage values for this sub-detail</p>
          </div>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Value
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Value</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="valueName">Value Name</Label>
                <Input
                  id="valueName"
                  value={newValueName}
                  onChange={(e) => setNewValueName(e.target.value)}
                  placeholder="Enter value name"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={createValueHandler}>Create</Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>


      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search values..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={handleSearchKeyPress}
              className="pl-10 w-64"
            />
          </div>
          <Button
            onClick={applySearch}
            size="sm"
            disabled={!searchTerm.trim()}
            className="bg-black text-white hover:bg-gray-800"
          >
            Search
          </Button>
          {appliedSearchTerm && (
            <Button
              onClick={clearSearch}
              variant="ghost"
              size="sm"
            >
              Clear
            </Button>
          )}
        </div>

      </div>


      <Card>
        <CardHeader>
          <CardTitle>Values ({totalRecords})</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <DataTable
            columns={columns}
            data={values}
            totalItems={totalRecords}
            totalPages={totalPages}
            currentPage={currentPage}
            pageSize={pageSize}
            onPageChange={(page) => {
              setCurrentPage(page);
            }}
            isLoading={loading}
          />
        </CardContent>
      </Card>


      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Value</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="editValueName">Value Name</Label>
              <Input
                id="editValueName"
                value={newValueName}
                onChange={(e) => setNewValueName(e.target.value)}
                placeholder="Enter value name"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="editValueActive"
                checked={newValueActive}
                onCheckedChange={(checked) => setNewValueActive(!!checked)}
              />
              <Label htmlFor="editValueActive">Active</Label>
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={updateValueHandler}>Update</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>


      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Delete</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p>Are you sure you want to delete the value <strong>&quot;{deletingValue?.name}&quot;</strong>?</p>
            <p className="text-sm text-gray-600">This action cannot be undone.</p>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsDeleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={confirmDelete}
              >
                Delete
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
