"use client";

import React, { useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { useDispatch, useSelector } from "react-redux";
import { completeForm, FormId } from "@/store/slices/formProgressSlice";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { axiosInstance } from "@/lib/axios";
import { useRouter } from "next/navigation";
import { RootState } from "@/store";
import { fetchClassDetails } from "@/store/thunks/classThunks";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Trash2 } from "lucide-react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const educationItemSchema = z.object({
  university: z.string().min(2, "University is required"),
  degree: z.string().min(2, "Degree is required"),
  degreeType: z.string().min(2, "Degree Type is required"),
  passoutYear: z.string().regex(/^\d{4}$/, "Enter a valid year (e.g., 2022)"),
  certificate: z.custom<any>(
    (val) => val instanceof FileList && val.length > 0,
    {
      message: "Degree certificate is required",
    }
  ),
});

const schema = z.object({
  noDegree: z.boolean().optional(),
  education: z.array(educationItemSchema).optional(),
});

type FormValues = z.infer<typeof schema>;

export function EducationForm() {
  const [noDegree, setNoDegree] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [degreeOptions, setDegreeOptions] = useState<string[]>([]);

  const form = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      noDegree: false,
      education: [
        {
          university: "",
          degree: "",
          degreeType: "",
          passoutYear: "",
          certificate: undefined,
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "education",
  });
  const { user }: any = useSelector((state: RootState) => state.user);
  const classData = useSelector((state: RootState) => state.class.classData);
  const dispatch = useDispatch();
  const router = useRouter();

  const handleNoDegreeSubmit = async () => {
    const formData = new FormData();
    formData.append("noDegree", "true");

    try {
      await axiosInstance.post(
        `/classes-profile/education`,
        formData,
        {
          headers: { "Content-Type": "multipart/form-data" },
        }
      );
      await dispatch(fetchClassDetails(user.id));
      toast.success("No degree status saved");
      dispatch(completeForm(FormId.EDUCATION));
      router.push("/classes/profile/experience");
    } catch (error) {
      toast.error("Something went wrong");
      console.log(error);
    }
  };

  const handleNoDegreeUncheck = async () => {
    const formData = new FormData();
    formData.append("noDegree", "false");

    try {
      await axiosInstance.post(
        `/classes-profile/education`,
        formData,
        {
          headers: { "Content-Type": "multipart/form-data" },
        }
      );
      await dispatch(fetchClassDetails(user.id));
      toast.success("You can now add your education details");
    } catch (error) {
      toast.error("Something went wrong");
      console.log(error);
    }
  };

  const onSubmit = async (data: FormValues) => {
    if (data.noDegree) {
      handleNoDegreeSubmit();
      return;
    }

    if (!data.education || data.education.length === 0) {
      toast.error("Please add at least one education record");
      return;
    }

    const formData = new FormData();
    formData.append("noDegree", "false");

    formData.append("education", JSON.stringify(data.education));

    data.education.forEach((edu) => {
      if (edu.certificate instanceof FileList) {
        formData.append("files", edu.certificate[0]);
      }
    });

    try {
      await axiosInstance.post(
        `/classes-profile/education`,
        formData,
        {
          headers: { "Content-Type": "multipart/form-data" },
        }
      );
      await dispatch(fetchClassDetails(user.id));
      toast.success("Education uploaded successfully");
      dispatch(completeForm(FormId.EDUCATION));
      router.push("/classes/profile/experience");
    } catch (error) {
      toast.error("Something went wrong");
      console.log(error)
    }
  };

  // Initialize form with existing data and fetch degree options
  React.useEffect(() => {
    const fetchDegreeOptions = async () => {
      try {
        const response = await axiosInstance.get("/constant/TuitionClasses");
        if (response.data && response.data.details) {
          const educationDetail = response.data.details.find((detail: any) => detail.name === 'Education');
          if (educationDetail && educationDetail.subDetails) {
            const degreeSubDetail = educationDetail.subDetails.find((subDetail: any) => subDetail.name === 'Degree');
            if (degreeSubDetail && degreeSubDetail.values) {
              const degrees = degreeSubDetail.values.map((value: any) => value.name);
              setDegreeOptions(degrees);
            }
          }
        }
      } catch (error) {
        console.error("Failed to fetch degree options:", error);
        toast.error("Failed to load degree options");
      }
    };

    // Fetch degree options on component mount
    fetchDegreeOptions();

    if (classData && !isInitialized) {
      const hasDegreeSetToFalse = classData.education?.some((edu: any) => edu.isDegree === false);

      if (hasDegreeSetToFalse) {
        setNoDegree(true);
        form.setValue('noDegree', true);

        form.setValue('education', []);

        toast.info("You have selected 'I don't have a degree'. You cannot add education data unless you uncheck this option.");
      }

      setIsInitialized(true);
    }
  }, [classData, form, isInitialized]);

  const handleDeleteEducation = async (eduId: string, classId: string) => {
    try {
      await axiosInstance.delete(`/classes-profile/education/${eduId}`, {
        data: { classId }
      });
      toast.success("Education deleted successfully");
      await dispatch(fetchClassDetails(classId));

      form.reset({
        noDegree: false,
        education: [
          {
            university: "",
            degree: "",
            degreeType: "",
            passoutYear: "",
            certificate: undefined,
          },
        ],
      });
    } catch (error) {
      toast.error("Failed to delete education");
      console.log(error)
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* No Degree Checkbox */}
        <FormField
          control={form.control}
          name="noDegree"
          render={({ field }) => (
            <FormItem className="flex items-center space-x-2">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={(checked) => {
                    field.onChange(checked);
                    setNoDegree(!!checked);

                    if (checked) {
                      handleNoDegreeSubmit();
                    } else {
                      handleNoDegreeUncheck();
                    }
                  }}
                />
              </FormControl>
              <FormLabel className="font-medium">
                I dont have a degree
              </FormLabel>
            </FormItem>
          )}
        />

        {classData?.education?.length > 0 && !noDegree && (
          <div className="space-y-4 mb-6">
            <h3 className="text-lg font-semibold">Previous Education</h3>

            {classData.education.map((edu: any, idx: any) => (
              <Card key={idx} className="bg-muted/30 relative">
                <CardHeader className="flex flex-row items-start justify-between">
                  <CardTitle className="text-base font-semibold">
                    Education #{idx + 1}
                  </CardTitle>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="text-red-500 cursor-pointer hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[425px]">
                      <DialogHeader>
                        <DialogTitle>Delete Education</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to delete this education record? This action cannot be undone.
                        </DialogDescription>
                      </DialogHeader>
                      <DialogFooter className="gap-2">
                        <Button
                          variant="outline"
                          onClick={() => (document.querySelector('button[data-state="open"]') as any).click()}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={() => {
                            handleDeleteEducation(edu.id, classData.id);
                            (document.querySelector('button[data-state="open"]') as any).click();
                            
                          }}
                        >
                          Delete
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </CardHeader>

                <CardContent className="space-y-3 text-sm">
                  <div>
                    <span className="font-medium">University:</span> {edu.university}
                  </div>
                  <div>
                    <span className="font-medium">Degree:</span> {edu.degree}
                  </div>
                  <div>
                    <span className="font-medium">Degree Type:</span> {edu.degreeType}
                  </div>
                  <div>
                    <span className="font-medium">Passout Year:</span> {edu.passoutYear}
                  </div>
                  {edu.certificate && (
                    <div>
                      <span className="font-medium">Certificate:</span>{" "}
                      <a
                        href={`${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}uploads/classes/${classData.id}/education/${edu.certificate}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 underline hover:text-blue-700"
                      >
                        View Certificate
                      </a>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        <h3 className="text-lg font-semibold">Add New Education</h3>
        {!noDegree &&
          fields.map((item, index) => (
            <div
              key={item.id}
              className="space-y-4 rounded-2xl border bg-muted/30 p-4 shadow-sm"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name={`education.${index}.university`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>University</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. Delhi University" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`education.${index}.degree`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Degree</FormLabel>
                      <FormControl>
                        <Select onValueChange={field.onChange} defaultValue={field.value} {...field}>
                          <SelectTrigger className="w-[300px]">
                            <SelectValue placeholder="Select Degree" />
                          </SelectTrigger>
                          <SelectContent>
                            {degreeOptions.map((degree) => (
                              <SelectItem key={degree} value={degree}>
                                {degree}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`education.${index}.degreeType`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Degree Type</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. Undergraduate" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`education.${index}.passoutYear`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Passout Year</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. 2020" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`education.${index}.certificate`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Degree Certificate (PDF/Image)</FormLabel>
                      <FormControl>
                        <Input
                          type="file"
                          accept=".pdf,.jpg,.jpeg,.png"
                          onChange={(e) => {
                            const files = e.target.files;
                            if (files && files.length > 0) {
                              const file = files[0];
                              const allowedTypes = ["application/pdf", "image/jpeg", "image/jpg", "image/png"];
                              if (!allowedTypes.includes(file.type)) {
                                toast.error("Only PDF and image files (.pdf, .jpg, .jpeg, .png) are allowed");
                                e.target.value = ""; // Clear the input
                                return;
                              }
                              field.onChange(files);
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {fields.length > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => remove(index)}
                  className="mt-2"
                >
                  Remove
                </Button>
              )}
            </div>
          ))}

        {/* Add new */}
        {!noDegree && (
          <Button
            type="button"
            variant="outline"
            onClick={() =>
              append({
                university: "",
                degree: "",
                degreeType: "",
                passoutYear: "",
                certificate: undefined,
              })
            }
            className="flex items-center gap-2"
          >
            Add New Education
          </Button>
        )}

        {/* Submit */}
        <Button type="submit">
          Save Education
        </Button>
      </form>
    </Form>
  );
}
