import prisma from '@/config/prismaClient';
import {
  Classes,
  ClassesAbout,
  ClassesEducation,
  ClassesExpereince,
  ClassesCertificates,
  TuitionClass,
  ClassApprovalStatus,
  UserType,
  NotificationType,
} from '@prisma/client';
import { createNotification, createAdminNotification } from '@/utils/notifications';
import {
  EducationData,
  ExperienceData,
  CertificateData,
  TuitionClassData,
  UpdateProfilePayload,
  CreateClassProfileData,
  UpdateProfileImagesData,
  UpdateDescriptionData,
} from '@/utils/interface';

export const createClassProfile = (data: CreateClassProfileData): Promise<ClassesAbout> => {
  return prisma.classesAbout.create({
    data: data,
  });
};

export const findClassProfileByEmail = (classId: string): Promise<ClassesAbout | null> => {
  return prisma.classesAbout.findUnique({ where: { classId } });
};

export const findClassProfileByUsername = (classId: string): Promise<ClassesAbout | null> => {
  return prisma.classesAbout.findUnique({ where: { classId } });
};

export const updateClassProfile = (
  id: string,
  data: Partial<{
    username: string;
    firstName: string;
    lastName: string;
    className: string;
    email: string;
    birthDate: Date;
    contactNo: string;
    catchyHeadline: string;
    tutorBio: string;
    profilePhoto: string;
    schoolLogo: string;
    videoUrl: string;
  }>
): Promise<ClassesAbout> => {
  return prisma.classesAbout.update({
    where: { id },
    data,
  });
};

export const findClassProfileById = (id: string): Promise<ClassesAbout | null> => {
  return prisma.classesAbout.findUnique({ where: { id } });
};

export const checkUniqueEmail = async (email: string, classId: string): Promise<Classes | null> => {
  return prisma.classes.findFirst({
    where: {
      email,
      id: {
        not: classId,
      },
    },
  });
};

export const updateClassesProfileService = async (
  payload: UpdateProfilePayload,
  classId: string
): Promise<void> => {
  const { username, firstName, lastName, className, contactNo, birthDate, email } = payload;

  await prisma.classes.update({
    where: { id: classId },
    data: {
      username,
      firstName: firstName,
      lastName: lastName,
      className: className,
      contactNo: contactNo,
      email,
    },
  });

  await prisma.classesAbout.upsert({
    where: { classId },
    update: {
      birthDate: new Date(birthDate),
    },
    create: {
      classId,
      birthDate: new Date(birthDate),
    },
  });
};

export const updateProfileImages = async (
  classId: string,
  data: UpdateProfileImagesData
): Promise<ClassesAbout> => {
  return prisma.classesAbout.update({
    where: { classId },
    data,
  });
};

export const updateDescriptionQuery = async (
  classId: string,
  data: UpdateDescriptionData
): Promise<ClassesAbout> => {
  return prisma.classesAbout.update({
    where: { classId },
    data,
  });
};

export const saveEducation = async (
  classId: string,
  educationData: EducationData[],
  filePaths: string[]
): Promise<{ count: number }> => {
  const payload = educationData.map((edu, i) => ({
    classId,
    university: edu.university,
    degree: edu.degree,
    degreeType: edu.degreeType,
    passoutYear: edu.passoutYear,
    certificate: filePaths[i],
    isDegree: true,
    status: 'PENDING' as const,
  }));

  const result = await prisma.classesEducation.createMany({ data: payload });

  // Create notification for education addition
  if (result.count > 0) {
    // Notification for class
    await createNotification({
      userId: classId,
      userType: UserType.CLASS,
      type: NotificationType.CLASS_EDUCATION_ADDED,
      title: 'Education Records Added',
      message: `You have successfully added ${result.count} education record(s) to your profile.`,
      data: {
        count: result.count,
        educationData: educationData.map(edu => ({
          university: edu.university,
          degree: edu.degree,
          degreeType: edu.degreeType,
          passoutYear: edu.passoutYear
        }))
      }
    });

    // Get class details for admin notification
    const classDetails = await prisma.classes.findUnique({
      where: { id: classId },
      select: { firstName: true, lastName: true, email: true }
    });

    // Notification for admin
    await createAdminNotification({
      type: NotificationType.ADMIN_PROFILE_REVIEW_REQUIRED,
      title: 'Class Added Education Records',
      message: `Class ${classDetails?.firstName} ${classDetails?.lastName} has added ${result.count} education record(s). Please review for approval.`,
      data: {
        classId: classId,
        className: `${classDetails?.firstName} ${classDetails?.lastName}`,
        email: classDetails?.email,
        count: result.count,
        type: 'education'
      }
    });
  }

  return result;
};

export const saveNoDegreeRecord = async (classId: string): Promise<ClassesEducation> => {
  await prisma.classesEducation.deleteMany({
    where: { classId },
  });
  return prisma.classesEducation.create({
    data: {
      classId,
      isDegree: false,
    },
  });
};

export const clearNoDegreeStatus = async (classId: string): Promise<{ count: number }> => {
  return prisma.classesEducation.deleteMany({
    where: {
      classId,
      isDegree: false,
    },
  });
};

export const saveExperience = async (
  classId: string,
  experienceData: ExperienceData[],
  filePaths: string[]
): Promise<{ count: number }> => {
  const payload = experienceData.map((exe, i) => ({
    classId,
    title: exe.title,
    from: exe.from,
    to: exe.to,
    certificateUrl: filePaths[i],
    isExperience: true,
    status: 'PENDING' as const,
  }));

  const result = await prisma.classesExpereince.createMany({ data: payload });

  // Create notification for experience addition
  if (result.count > 0) {
    // Notification for class
    await createNotification({
      userId: classId,
      userType: UserType.CLASS,
      type: NotificationType.CLASS_EXPERIENCE_ADDED,
      title: 'Experience Records Added',
      message: `You have successfully added ${result.count} experience record(s) to your profile.`,
      data: {
        count: result.count,
        experienceData: experienceData.map(exp => ({
          title: exp.title,
          from: exp.from,
          to: exp.to
        }))
      }
    });

    // Get class details for admin notification
    const classDetails = await prisma.classes.findUnique({
      where: { id: classId },
      select: { firstName: true, lastName: true, email: true }
    });

    // Notification for admin
    await createAdminNotification({
      type: NotificationType.ADMIN_PROFILE_REVIEW_REQUIRED,
      title: 'Class Added Experience Records',
      message: `Class ${classDetails?.firstName} ${classDetails?.lastName} has added ${result.count} experience record(s). Please review for approval.`,
      data: {
        classId: classId,
        className: `${classDetails?.firstName} ${classDetails?.lastName}`,
        email: classDetails?.email,
        count: result.count,
        type: 'experience'
      }
    });
  }

  return result;
};

export const saveNoExperienceRecord = async (classId: string): Promise<ClassesExpereince> => {
  await prisma.classesExpereince.deleteMany({
    where: { classId },
  });
  return prisma.classesExpereince.create({
    data: {
      classId,
      isExperience: false,
    },
  });
};

export const clearNoExperienceStatus = async (classId: string): Promise<{ count: number }> => {
  return prisma.classesExpereince.deleteMany({
    where: {
      classId,
      isExperience: false,
    },
  });
};

export const saveCertificate = async (
  classId: string,
  certificateData: CertificateData[],
  filePaths: string[]
): Promise<{ count: number }> => {
  const payload = certificateData.map((cert, i) => ({
    classId,
    title: cert.title,
    certificateUrl: filePaths[i],
    isCertificate: true,
    status: 'PENDING' as const,
  }));

  const result = await prisma.classesCertificates.createMany({ data: payload });

  // Create notification for certificate addition
  if (result.count > 0) {
    // Notification for class
    await createNotification({
      userId: classId,
      userType: UserType.CLASS,
      type: NotificationType.CLASS_CERTIFICATE_ADDED,
      title: 'Certificates Added',
      message: `You have successfully added ${result.count} certificate(s) to your profile.`,
      data: {
        count: result.count,
        certificateData: certificateData.map(cert => ({
          title: cert.title
        }))
      }
    });

    // Get class details for admin notification
    const classDetails = await prisma.classes.findUnique({
      where: { id: classId },
      select: { firstName: true, lastName: true, email: true }
    });

    // Notification for admin
    await createAdminNotification({
      type: NotificationType.ADMIN_PROFILE_REVIEW_REQUIRED,
      title: 'Class Added Certificates',
      message: `Class ${classDetails?.firstName} ${classDetails?.lastName} has added ${result.count} certificate(s). Please review for approval.`,
      data: {
        classId: classId,
        className: `${classDetails?.firstName} ${classDetails?.lastName}`,
        email: classDetails?.email,
        count: result.count,
        type: 'certificate'
      }
    });
  }

  return result;
};

export const saveNoCertificateRecord = async (classId: string): Promise<ClassesCertificates> => {
  await prisma.classesCertificates.deleteMany({
    where: { classId },
  });
  return prisma.classesCertificates.create({
    data: {
      classId,
      isCertificate: false,
    },
  });
};

export const clearNoCertificateStatus = async (classId: string): Promise<{ count: number }> => {
  return prisma.classesCertificates.deleteMany({
    where: {
      classId,
      isCertificate: false,
    },
  });
};

export const checkUniqueUserName = async (
  username: string,
  classId: string
): Promise<Classes | null> => {
  return prisma.classes.findFirst({
    where: {
      username,
      NOT: {
        id: classId,
      },
    },
  });
};

export const sendClassForReview = async (classId: string): Promise<{ message: string }> => {
  const existingClass = await prisma.classes.findUnique({
    where: { id: classId },
  });

  if (!existingClass) {
    throw new Error('Class not found');
  }

  await prisma.classesStatus.upsert({
    where: { classId },
    update: { status: 'PENDING' as ClassApprovalStatus },
    create: {
      classId,
      status: 'PENDING' as ClassApprovalStatus,
    },
  });

  // Create notification for admin about profile review required
  await createAdminNotification({
    type: NotificationType.ADMIN_PROFILE_REVIEW_REQUIRED,
    title: 'Profile Review Required',
    message: `Class profile for ${existingClass.firstName} ${existingClass.lastName} has been submitted for review.`,
    data: {
      classId: classId,
      className: `${existingClass.firstName} ${existingClass.lastName}`,
      email: existingClass.email
    }
  });

  return { message: 'Class profile sent for review' };
};

export const createTuitionClass = async (
  classId: string,
  tuitionClassData: TuitionClassData
): Promise<TuitionClass[]> => {
  const { tuitionDetails } = tuitionClassData;

  if (!Array.isArray(tuitionDetails) || tuitionDetails.length === 0) {
    throw new Error('No tuition class details provided');
  }

  try {
    return await Promise.all(
      tuitionDetails.map(async (detail) => {
        return await prisma.tuitionClass.create({
          data: {
            classId,
            boardType: Array.isArray(detail.boardType)
              ? JSON.stringify(detail.boardType)
              : detail.boardType,
            subject: Array.isArray(detail.subject)
              ? JSON.stringify(detail.subject)
              : detail.subject,
            education: detail.education,
            medium: Array.isArray(detail.medium) ? JSON.stringify(detail.medium) : detail.medium,
            section: Array.isArray(detail.section)
              ? JSON.stringify(detail.section)
              : detail.section,
            coachingType: Array.isArray(detail.coachingType)
              ? JSON.stringify(detail.coachingType)
              : detail.coachingType,
            details: Array.isArray(detail.details)
              ? JSON.stringify(detail.details)
              : detail.details,
          },
        });
      })
    );
  } catch (error) {
    console.error('Failed to create tuition classes:', error);
    throw error;
  }
};

export const deleteEducationRecord = async (id: string): Promise<ClassesEducation> => {
  return prisma.classesEducation.delete({
    where: { id },
  });
};

export const deleteExperienceRecord = async (id: string): Promise<ClassesExpereince> => {
  return prisma.classesExpereince.delete({
    where: { id },
  });
};

export const deleteCertificateRecord = async (id: string): Promise<ClassesCertificates> => {
  return prisma.classesCertificates.delete({
    where: { id },
  });
};

export const deleteTuitionClassRecord = async (id: string): Promise<TuitionClass> => {
  return prisma.tuitionClass.delete({
    where: { id },
  });
};

// Status update functions for Experience, Education, and Certificates
export const updateExperienceStatus = async (
  id: string,
  status: 'PENDING' | 'APPROVED' | 'REJECTED'
): Promise<ClassesExpereince> => {
  return prisma.classesExpereince.update({
    where: { id },
    data: { status },
    include: { class: true },
  });
};

export const updateEducationStatus = async (
  id: string,
  status: 'PENDING' | 'APPROVED' | 'REJECTED'
): Promise<ClassesEducation> => {
  return prisma.classesEducation.update({
    where: { id },
    data: { status },
    include: { class: true },
  });
};

export const updateCertificateStatus = async (
  id: string,
  status: 'PENDING' | 'APPROVED' | 'REJECTED'
): Promise<ClassesCertificates> => {
  return prisma.classesCertificates.update({
    where: { id },
    data: { status },
    include: { class: true },
  });
};



