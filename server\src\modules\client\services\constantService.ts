import prisma from '@/config/prismaClient';

export const getAllConstants = async () => {
  return await prisma.constantCategory.findMany({
    include: {
      details: true,
    },
  });
};

export const getConstantsByCategory = async (category: string) => {
  return await prisma.constantCategory.findFirst({
    where: {
      name: category,
    },
    include: {
      details: {
        include: {
          subDetails: {
            include: {
              values: true,
            },
          },
        },
      },
    },
  });
};
