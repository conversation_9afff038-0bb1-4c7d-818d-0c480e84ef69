"use client";

import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import { Thought } from '@/services/classesThoughtApi';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { Quote } from 'lucide-react';
import { Variants, Transition } from 'framer-motion';

const swiperStyles = `
  .swiper-container {
    position: relative;
  }
  .swiper-pagination {
    position: absolute;
    bottom: 20px !important;
  }
  .swiper-pagination-bullet {
    background: #d1d5db;
    opacity: 0.5;
    width: 12px;
    height: 12px;
    margin: 0 6px !important;
    border-radius: 12px;
    transition: all 0.3s ease;
  }
  .swiper-pagination-bullet-active {
    background: #FD904B;
    opacity: 0.5;
    width: 36px;
    border-radius: 12px;
    transform: none;
  }
`;

interface ThoughtSliderProps {
  thoughts: Thought[];
}

const ThoughtSlider: React.FC<ThoughtSliderProps> = ({ thoughts }) => {
  const baseUrl = (process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005').replace(/\/+$/, '');

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {

    e.currentTarget.style.display = 'none';
  };

  const slideVariants: Variants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      } as Transition,
    },
  };

  const contentVariants: Variants = {
    hidden: { opacity: 0, x: -20 },
    visible: (i: number) => ({
      opacity: 1,
      x: 0,
      transition: {
        delay: i * 0.2,
        duration: 0.5,
        ease: 'easeOut',
      } as Transition,
    }),
  };

  const approvedThoughts = thoughts.filter(thought => thought.status === 'APPROVED');

  return (
    <>
      <style>{swiperStyles}</style>
      <Swiper
        modules={[Navigation, Pagination, Autoplay]}
        spaceBetween={30}
        slidesPerView={1}
        navigation={{
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        }}
        pagination={{ clickable: true }}
        autoplay={{ delay: 4000, disableOnInteraction: false }}
        className="w-full max-w-6xl mx-auto swiper-container"
      >
        {approvedThoughts.map((thought) => {
          const imagePath = thought.class.ClassAbout?.classesLogo
            ? `${baseUrl}${thought.class.ClassAbout.classesLogo.startsWith('/') ? '' : '/'}${thought.class.ClassAbout.classesLogo}`
            : '';

          return (
            <SwiperSlide key={thought.id}>
              <motion.div
                variants={slideVariants}
                initial="hidden"
                animate="visible"
                className="relative dark:bg-siderbar rounded-2xl p-8 sm:p-8 flex flex-col md:flex-row items-center gap-6 sm:gap-8 overflow-hidden border border-gray-200 dark:border-gray-700/50 backdrop-blur-lg shadow-sm mb-12"
              >

                {/* Decorative Quote Icon */}
                <div className="absolute top-4 left-4 opacity-20">
                  <Quote className="w-12 h-12 text-[#FD904B]" />
                </div>

                {/* Logo Image */}
                <div className="flex-shrink-0 relative">
                  <motion.div
                    transition={{ duration: 0.3 }}
                    className="h-24 w-24 sm:h-28 sm:w-28 rounded-full overflow-hidden border-4 border-[#FD904B]/20 shadow-sm"
                  >
                    {imagePath ? (
                      <Image
                        width={200}
                        height={200}
                        src={imagePath}
                        alt="Class Logo"
                        className="h-full w-full object-cover"
                        onError={handleImageError}
                      />
                    ) : (
                      <div className="h-full w-full flex items-center justify-center bg-gray-100 dark:bg-gray-700">
                        <span className="text-gray-400 dark:text-gray-500 text-xs">No logo</span>
                      </div>
                    )}
                  </motion.div>
                </div>

                {/* Thought Content */}
                <div className="flex-1 text-center md:text-left space-y-3 relative z-10">
                  <motion.p
                    custom={0}
                    variants={contentVariants}
                    initial="hidden"
                    animate="visible"
                    className="text-xl sm:text-2xl font-semibold text-gray-900 dark:text-gray-50 leading-tight tracking-wide"
                  >
                    &quot;{thought.thoughts}&quot;
                  </motion.p>
                  <motion.p
                    custom={1}
                    variants={contentVariants}
                    initial="hidden"
                    animate="visible"
                    className="text-lg font-medium text-gray-700 dark:text-gray-300"
                  >
                    {thought.class.className}
                  </motion.p>
                  <motion.p
                    custom={2}
                    variants={contentVariants}
                    initial="hidden"
                    animate="visible"
                    className="text-md font-light text-gray-600 dark:text-gray-400 italic"
                  >
                    — {thought.class.firstName} {thought.class.lastName}
                  </motion.p>
                </div>
              </motion.div>
            </SwiperSlide>
          );
        })}

      </Swiper>
    </>
  );
};

export default ThoughtSlider;