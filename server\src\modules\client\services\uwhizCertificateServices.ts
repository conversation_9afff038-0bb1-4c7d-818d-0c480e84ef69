import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface Participant {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

export const getExamParticipants = async (
  examId?: number,
  classId?: string
): Promise<Participant[]> => {
  try {
    const participants = await prisma.question_answer.findMany({
      where: {
        userId: classId,
        question: {
          examId,
        },
      },
      select: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      distinct: ['userId'],
    });

    return participants.map((p) => ({
      ...p.user,
      email: p.user.email ?? ''
    }));
  } catch (error) {
    console.error('Error fetching exam participants:', error);
    throw new Error('');
  }
};
