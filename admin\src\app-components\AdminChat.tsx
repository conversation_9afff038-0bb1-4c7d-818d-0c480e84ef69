'use client';

import { useState, useEffect, useCallback } from 'react';
import {
    Avatar,
    AvatarFallback,
} from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Search,
    Users,
    GraduationCap,
    UserCheck,
    MessageSquare,
    Loader2,
    RefreshCw,
} from 'lucide-react';
import { AdminChatProps, ChatMessage, Class, Student } from '@/lib/types';
import { format } from 'date-fns';
import * as chatApi from '@/services/chatApi';
import { toast } from 'sonner';

export default function AdminChat({ isAuthenticated }: AdminChatProps) {
    const [classSearchQuery, setClassSearchQuery] = useState('');
    const [studentSearchQuery, setStudentSearchQuery] = useState('');
    const [selectedUser, setSelectedUser] = useState<string | null>(null);
    const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
    const [selectedClass, setSelectedClass] = useState<Class | null>(null);
    const [classes, setClasses] = useState<Class[]>([]);
    const [students, setStudents] = useState<Student[]>([]);
    const [messages, setMessages] = useState<ChatMessage[]>([]);
    const [loading, setLoading] = useState(false);
    const [refreshing, setRefreshing] = useState(false);
    const [classesLoading, setClassesLoading] = useState(false);
    const [studentsLoading, setStudentsLoading] = useState(false);
    const [classesPage, setClassesPage] = useState(1);
    const [classesHasNext, setClassesHasNext] = useState(false);
    const [studentsPage, setStudentsPage] = useState(1);
    const [studentsHasNext, setStudentsHasNext] = useState(false);
    const loadClasses = useCallback(async (page: number = 1, reset: boolean = false) => {
        try {
            if (page === 1) setLoading(true);
            else setClassesLoading(true);

            const response = await chatApi.getClasses(page, 10, classSearchQuery);

            if (reset || page === 1) {
                setClasses(response.data);
                setClassesPage(1);
            } else {
                setClasses(prev => [...prev, ...response.data]);
            }

            setClassesHasNext(response.pagination.hasNext);
            setClassesPage(page);
        } catch {
            toast.error('Failed to load classes');
        } finally {
            setLoading(false);
            setClassesLoading(false);
        }
    }, [classSearchQuery]);
     
    const loadStudents = useCallback(async (classId: string, page: number = 1, reset: boolean = false) => {
        try {
            if (page === 1) setLoading(true);
            else setStudentsLoading(true);

            const response = await chatApi.getStudentsForClass(classId, page, 10, studentSearchQuery);

            if (reset || page === 1) {
                setStudents(response.data);
                setStudentsPage(1);
            } else {
                setStudents(prev => [...prev, ...response.data]);
            }

            setStudentsHasNext(response.pagination.hasNext);
            setStudentsPage(page);
        } catch (error: any) {
            toast.error(`Failed to load students: ${error.response?.data?.error || error.message}`);
            if (reset || page === 1) setStudents([]);
        } finally {
            setLoading(false);
            setStudentsLoading(false);
        }
    }, [studentSearchQuery]);
    useEffect(() => {
        if (isAuthenticated) {
            loadClasses(1, true);
            setStudents([]);
        }
    }, [isAuthenticated, loadClasses]);

    useEffect(() => {
        if (isAuthenticated && classes.length > 0) {
            const timeoutId = setTimeout(() => {
                setClassesPage(1);
                loadClasses(1, true);
            }, 300);
            return () => clearTimeout(timeoutId);
        }
    }, [classSearchQuery, isAuthenticated, loadClasses]);

    useEffect(() => {
        if (selectedClass && students.length > 0) {
            const timeoutId = setTimeout(() => {
                setStudentsPage(1);
                loadStudents(selectedClass.id, 1, true);
            }, 300);
            return () => clearTimeout(timeoutId);
        }
    }, [studentSearchQuery, selectedClass, loadStudents]);

    const handleClassSelect = async (classItem: Class) => {
        setSelectedClass(classItem);
        setSelectedUser(null);
        setSelectedUserId(null);
        setMessages([]);
        setStudentSearchQuery('');

        setStudentsPage(1);
        await loadStudents(classItem.id, 1, true);
    };

    const handleStudentSelect = async (student: Student) => {
        if (!selectedClass) return;

        setSelectedUser(`${student.firstName} ${student.lastName}`);
        setSelectedUserId(student.id);

        try {
            const conversation = await chatApi.getConversationBetween(selectedClass.id, student.id);
            setMessages(conversation?.messages || []);
        } catch (error: any) {
            toast.error('Failed to load conversation');
            console.log(error)
            setMessages([]);
        }
    };

    const formatTime = (timestamp: string) => {
        return format(new Date(timestamp), 'h:mm a');
    };

    const handleRefresh = async () => {
        setRefreshing(true);
        try {
            await loadClasses(1, true);

            if (selectedClass) {
                await loadStudents(selectedClass.id, 1, true);
            }

            if (selectedClass && selectedUserId) {
                const conversation = await chatApi.getConversationBetween(selectedClass.id, selectedUserId);
                setMessages(conversation?.messages || []);
            }

            toast.success('Chat data refreshed successfully');
        } catch (error: any) {
            console.log(error)
            toast.error('Failed to refresh data');
        } finally {
            setRefreshing(false);
        }
    };

    const handleLoadMoreClasses = async () => {
        if (classesHasNext && !classesLoading && !loading) {
            await loadClasses(classesPage + 1, false);
        }
    };

    const handleLoadMoreStudents = async () => {
        if (studentsHasNext && !studentsLoading && !loading && selectedClass) {
            await loadStudents(selectedClass.id, studentsPage + 1, false);
        }
    };

    return (
        <div className="h-[calc(100vh-64px)] bg-background text-foreground flex">
            <aside className="border-r w-80 flex flex-col h-full">
                <div className="p-4 flex items-center justify-between border-b-2 border-gray-200 bg-white">
                    <h2 className="text-lg font-semibold flex items-center gap-2">
                        <GraduationCap className="h-5 w-5" />
                        <span>Classes</span>
                    </h2>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRefresh}
                        disabled={refreshing}
                        className="bg-white border-2 border-gray-300  text-black hover:bg-gray-100  px-3 py-2 rounded-xl font-medium transition-all duration-300"
                    >
                        {refreshing ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                            <RefreshCw className="h-4 w-4" />
                        )}
                    </Button>
                </div>

                <div className="p-4">
                    <div className="relative">
                        <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500 dark:text-gray-400" />
                        <Input
                            placeholder="Search classes..."
                            className="pl-12 pr-4 py-3 bg-gray-50 border-2 border-gray-200 dark:border-gray-700 rounded-xl text-black dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-black dark:focus:border-white focus:ring-0 transition-all duration-300"
                            value={classSearchQuery}
                            onChange={(e) => setClassSearchQuery(e.target.value)}
                        />
                    </div>
                </div>

                <div className="flex-1 overflow-y-auto overscroll-contain">
                    <div className="divide-y">
                        {loading ? (
                            <div className="text-center py-8">
                                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                                <p className="text-muted-foreground">Loading classes...</p>
                            </div>
                        ) : classes.length === 0 ? (
                            <div className="p-3 sm:p-4 text-center text-muted-foreground">
                                <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                <p>No classes found</p>
                                {classSearchQuery && (
                                    <p className="text-xs mt-2">No classes match {classSearchQuery}</p>
                                )}
                            </div>
                        ) : (
                            <>
                                {classes.map((classItem) => (
                                    <div
                                        key={classItem.id}
                                        onClick={() => handleClassSelect(classItem)}
                                        className={`mx-3 mb-3 p-4 rounded-xl transition-all duration-300 cursor-pointer border-2 hover:shadow-lg ${selectedClass?.id === classItem.id
                                                ? 'bg-black dark:bg-white text-white border-black dark:border-white shadow-xl'
                                                : 'bg-white border-gray-200 hover:bg-gray-50'
                                            }`}
                                    >
                                        <div className="flex gap-3 items-center">
                                            <div className="relative">
                                                <Avatar className="border-2 border-gray-300 shadow-md h-12 w-12">
                                                    <AvatarFallback className={`text-sm font-semibold ${selectedClass?.id === classItem.id
                                                            ? 'bg-white dark:bg-black text-black '
                                                            : 'bg-gray-100  text-black '
                                                        }`}>
                                                        {(classItem.firstName || 'C').substring(0, 1)}{(classItem.lastName || 'L').substring(0, 1)}
                                                    </AvatarFallback>
                                                </Avatar>
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex justify-between items-center">
                                                    <h3 className={`font-semibold truncate text-base ${selectedClass?.id === classItem.id
                                                            ? 'text-white dark:text-black'
                                                            : 'text-black dark:text-white'
                                                        }`}>
                                                        {classItem.firstName || 'Unknown'} {classItem.lastName || 'Class'}
                                                    </h3>
                                                </div>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <p className={`text-xs font-medium truncate ${selectedClass?.id === classItem.id
                                                            ? 'text-gray-200'
                                                            : 'text-gray-600'
                                                        }`}>
                                                        <GraduationCap className="h-3 w-3 inline mr-1" />
                                                        Teacher • {classItem.email || 'No email'}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}

                                {classesHasNext && (
                                    <div className="p-4 text-center">
                                        <Button
                                            onClick={handleLoadMoreClasses}
                                            disabled={classesLoading || loading}
                                            variant="outline"
                                            className="bg-white border-2 border-gray-300 text-black hover:bg-gray-100 px-4 py-2 rounded-xl font-medium transition-all duration-300"
                                        >
                                            {classesLoading ? (
                                                <>
                                                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                                    Loading more classes...
                                                </>
                                            ) : (
                                                'Load More Classes'
                                            )}
                                        </Button>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                </div>
            </aside>

            <aside className="border-r w-80 flex flex-col h-full">
                <div className="p-4 border-b-2 border-gray-200 bg-white">
                    <h2 className="text-lg font-semibold flex items-center gap-2 mb-4">
                        <UserCheck className="h-5 w-5" />
                        <span>Students</span>
                    </h2>
                    {selectedClass && (
                        <div className="mb-3 p-3 bg-gray-50 rounded-xl border-2 border-gray-200">
                            <p className="text-xs text-gray-600">Students who chatted with:</p>
                            <p className="font-medium text-sm text-black">{selectedClass.firstName || 'Unknown'} {selectedClass.lastName || 'Class'}</p>
                        </div>
                    )}
                    <div className="relative">
                        <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500" />
                        <Input
                            placeholder="Search students..."
                            className="pl-12 pr-4 py-3 bg-gray-50 dark:bg-gray-900 border-2 border-gray-200 dark:border-gray-700 rounded-xl text-black dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-black dark:focus:border-white focus:ring-0 transition-all duration-300"
                            value={studentSearchQuery}
                            onChange={(e) => setStudentSearchQuery(e.target.value)}
                        />
                    </div>
                </div>

                <div className="flex-1 overflow-y-auto overscroll-contain">
                    <div className="divide-y dark:divide-gray-700">
                        {loading ? (
                            <div className="text-center py-8">
                                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                                <p className="text-muted-foreground">Loading students...</p>
                            </div>
                        ) : students.length === 0 ? (
                            <div className="p-3 sm:p-4 text-center text-muted-foreground">
                                <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                {!selectedClass ? (
                                    <>
                                        <p>No students available</p>
                                        <p className="text-xs mt-2">Select a class first to see students who have chatted</p>
                                    </>
                                ) : (
                                    <>
                                        <p>No students have chatted with this class yet</p>
                                        <p className="text-xs mt-2">Students will appear here once they start chatting with {selectedClass.firstName || 'Unknown'} {selectedClass.lastName || 'Class'}</p>
                                    </>
                                )}
                                {studentSearchQuery && (
                                    <p className="text-xs mt-2">No students match {studentSearchQuery}</p>
                                )}
                            </div>
                        ) : (
                            <>
                                {students.map((student) => (
                                    <div
                                        key={student.id}
                                        onClick={() => handleStudentSelect(student)}
                                        className={`mx-3 mb-3 p-4 rounded-xl transition-all duration-300 cursor-pointer border-2 hover:shadow-lg ${selectedUserId === student.id
                                                ? 'bg-black dark:bg-white text-white dark:text-black border-black dark:border-white shadow-xl'
                                                : 'bg-white dark:bg-black border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-900'
                                            }`}
                                    >
                                        <div className="flex gap-3 items-center">
                                            <div className="relative">
                                                <Avatar className="border-2 border-gray-300 dark:border-gray-600 shadow-md h-12 w-12">
                                                    <AvatarFallback className={`text-sm font-semibold ${selectedUserId === student.id
                                                            ? 'bg-white dark:bg-black text-black dark:text-white'
                                                            : 'bg-gray-100 dark:bg-gray-800 text-black dark:text-white'
                                                        }`}>
                                                        {(student.firstName || 'S').substring(0, 1)}{(student.lastName || 'T').substring(0, 1)}
                                                    </AvatarFallback>
                                                </Avatar>
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex justify-between items-center">
                                                    <h3 className={`font-semibold truncate text-base ${selectedUserId === student.id
                                                            ? 'text-white dark:text-black'
                                                            : 'text-black dark:text-white'
                                                        }`}>
                                                        {student.firstName || 'Unknown'} {student.lastName || 'Student'}
                                                    </h3>
                                                </div>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <p className={`text-xs font-medium truncate ${selectedUserId === student.id
                                                            ? 'text-gray-200 dark:text-gray-700'
                                                            : 'text-gray-600 dark:text-gray-400'
                                                        }`}>
                                                        <UserCheck className="h-3 w-3 inline mr-1" />
                                                        Student • {student.email || 'No email'}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}

                                {studentsHasNext && (
                                    <div className="p-4 text-center">
                                        <Button
                                            onClick={handleLoadMoreStudents}
                                            disabled={studentsLoading || loading}
                                            variant="outline"
                                            className="bg-white border-2 border-gray-300 text-black hover:bg-gray-100 px-4 py-2 rounded-xl font-medium transition-all duration-300"
                                        >
                                            {studentsLoading ? (
                                                <>
                                                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                                    Loading more students...
                                                </>
                                            ) : (
                                                'Load More Students'
                                            )}
                                        </Button>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                </div>
            </aside>

            <main className="flex-1 flex flex-col min-w-0 bg-white">
                <div className="p-4 border-b-2 border-gray-200 flex items-center gap-3 bg-white">
                    <div className="flex gap-3 items-center min-w-0 flex-1">
                        <div className="relative">
                            <Avatar className="border-2 border-gray-300 flex-shrink-0 shadow-md h-12 w-12">
                                {selectedUser && selectedClass ? (
                                    <AvatarFallback className="text-sm font-semibold bg-gray-100 dark:bg-gray-800 text-black dark:text-white">
                                        {(selectedUser || 'US').substring(0, 2).toUpperCase()}
                                    </AvatarFallback>
                                ) : (
                                    <AvatarFallback className="bg-gray-100 dark:bg-gray-800">
                                        <Users className="h-6 w-6 text-gray-500 dark:text-gray-400" />
                                    </AvatarFallback>
                                )}
                            </Avatar>
                        </div>
                        <div className="min-w-0 flex-1">
                            <h1 className="font-semibold flex items-center gap-2 truncate text-black text-lg">
                                {selectedUser && selectedClass ? (
                                    <span className="truncate">{selectedClass.firstName || 'Unknown'} {selectedClass.lastName || 'Class'} ↔ {selectedUser || 'Unknown Student'}</span>
                                ) : 'Select a class and student'}
                            </h1>
                            <p className="text-gray-600 truncate text-sm">
                                {selectedUser && selectedClass ? (
                                    'Admin View'
                                ) : 'Choose a class and student to view their conversation'}
                            </p>
                        </div>
                    </div>
                </div>

                <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
                    <div className="space-y-4 max-w-4xl mx-auto">
                        {selectedUser && selectedClass ? (
                            messages.length > 0 ? (
                                messages.map((message) => {
                                    const isTeacher = message.senderType === 'class';
                                    const isStudent = message.senderType === 'student';

                                    return (
                                        <div
                                            key={message.id}
                                            className={`flex items-end gap-3 ${isStudent ? 'justify-end' : 'justify-start'}`}
                                        >
                                            {isTeacher && (
                                                <Avatar className="h-8 w-8 border-2 border-gray-300 shadow-sm">
                                                    <AvatarFallback className="text-xs bg-gray-200  text-black font-semibold">
                                                        <GraduationCap className="h-4 w-4" />
                                                    </AvatarFallback>
                                                </Avatar>
                                            )}

                                            <div className={`max-w-[70%] ${isStudent ? 'text-right' : 'text-left'}`}>
                                                <div
                                                    className={`${isTeacher
                                                            ? 'bg-white dark:bg-black text-black dark:text-white border-2 border-gray-200 dark:border-gray-700 rounded-2xl rounded-bl-md'
                                                            : isStudent
                                                                ? 'bg-black dark:bg-white text-white dark:text-black rounded-2xl rounded-br-md'
                                                                : 'bg-white dark:bg-black text-black dark:text-white border-2 border-gray-200 dark:border-gray-700 rounded-2xl'
                                                        } p-4 shadow-lg break-words`}
                                                >
                                                    <div className="text-base leading-relaxed">
                                                        {message.text}
                                                    </div>
                                                    <div className={`text-xs mt-2 flex items-end gap-1 ${
                                                        isStudent ? 'justify-start' : 'justify-end'
                                                    } ${
                                                        isTeacher
                                                            ? 'text-gray-500'
                                                            : isStudent
                                                                ? 'text-gray-300'
                                                                : 'text-gray-500'
                                                    }`}>
                                                        <span className="mr-2">
                                                            {isTeacher && <GraduationCap className="h-3 w-3 inline mr-1" />}
                                                            {isStudent && <UserCheck className="h-3 w-3 inline mr-1" />}
                                                            {isTeacher ? 'Teacher' : 'Student'}
                                                        </span>
                                                        {formatTime(message.timestamp)}
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Avatar for student (right side) */}
                                            {isStudent && (
                                                <Avatar className="h-8 w-8 border-2 border-gray-300 shadow-sm">
                                                    <AvatarFallback className="text-xs bg-gray-200 text-black font-semibold">
                                                        <UserCheck className="h-4 w-4" />
                                                    </AvatarFallback>
                                                </Avatar>
                                            )}
                                        </div>
                                    );
                                })
                            ) : (
                                <div className="flex flex-col items-center justify-center h-full py-12 text-center">
                                    <MessageSquare className="text-gray-400 mb-4 h-16 w-16" />
                                    <p className="text-gray-600 text-lg font-medium">No messages yet</p>
                                    <p className="text-gray-500 text-sm mt-2">No conversation between {selectedClass?.firstName || 'Unknown'} {selectedClass?.lastName || 'Class'} and {selectedUser || 'Unknown Student'}</p>
                                </div>
                            )
                        ) : (
                            <div className="flex flex-col items-center justify-center h-full py-12 text-center">
                                <MessageSquare className="text-gray-400 mb-4 h-16 w-16" />
                                <p className="text-gray-600 text-lg font-medium">Select a class and student</p>
                                <p className="text-gray-500 text-sm mt-2">Choose from the sidebars to view their conversation</p>
                            </div>
                        )}
                    </div>
                </div>
            </main>
        </div>
    );
}